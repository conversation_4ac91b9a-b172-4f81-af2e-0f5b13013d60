<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\Payment;
use App\Models\SubscriptionProduct;
use App\Models\Invoice;
use App\Models\Refund;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    /**
     * Display the admin dashboard with key metrics and analytics
     */
    public function index()
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => true
            ],
        ];

        // Key Metrics
        $totalUsers = User::count();
        $activeSubscriptions = UserSubscription::where('status', 'active')->count();
        $totalRevenue = Payment::where('status', 'succeeded')->sum('amount');
        $monthlyRevenue = Payment::where('status', 'succeeded')
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->sum('amount');

        // Recent Activity
        $recentUsers = User::latest()->take(5)->get();
        $recentSubscriptions = UserSubscription::with(['user', 'product'])
            ->latest()
            ->take(5)
            ->get();
        $recentPayments = Payment::with('user')
            ->latest()
            ->take(5)
            ->get();

        // Chart Data - Monthly Revenue for the last 12 months
        $monthlyRevenueData = [];
        $monthLabels = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthLabels[] = $date->format('M Y');
            $revenue = Payment::where('status', 'succeeded')
                ->whereMonth('created_at', $date->month)
                ->whereYear('created_at', $date->year)
                ->sum('amount');
            $monthlyRevenueData[] = $revenue / 100; // Convert from cents
        }

        // Subscription Status Distribution
        $subscriptionStats = UserSubscription::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Top Performing Products
        $topProducts = SubscriptionProduct::withCount('subscriptions')
            ->orderBy('subscriptions_count', 'desc')
            ->take(5)
            ->get();

        // Failed Payments Count
        $failedPayments = Payment::where('status', 'failed')
            ->whereMonth('created_at', Carbon::now()->month)
            ->count();

        // Churn Rate (cancelled subscriptions this month vs total active last month)
        $cancelledThisMonth = UserSubscription::where('status', 'cancelled')
            ->whereMonth('updated_at', Carbon::now()->month)
            ->count();
        
        $activeLastMonth = UserSubscription::where('status', 'active')
            ->whereMonth('created_at', '<=', Carbon::now()->subMonth()->endOfMonth())
            ->count();
        
        $churnRate = $activeLastMonth > 0 ? ($cancelledThisMonth / $activeLastMonth) * 100 : 0;

        return view('admin.dashboard.index', [
            'pageTitle' => 'Admin Dashboard',
            'breadcrumbItems' => $breadcrumbsItems,
            'metrics' => [
                'totalUsers' => $totalUsers,
                'activeSubscriptions' => $activeSubscriptions,
                'totalRevenue' => $totalRevenue / 100, // Convert from cents
                'monthlyRevenue' => $monthlyRevenue / 100,
                'failedPayments' => $failedPayments,
                'churnRate' => round($churnRate, 2)
            ],
            'chartData' => [
                'monthlyRevenue' => [
                    'labels' => $monthLabels,
                    'data' => $monthlyRevenueData
                ],
                'subscriptionStats' => $subscriptionStats
            ],
            'recentActivity' => [
                'users' => $recentUsers,
                'subscriptions' => $recentSubscriptions,
                'payments' => $recentPayments
            ],
            'topProducts' => $topProducts
        ]);
    }
}
