<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SubscriptionProduct;
use App\Models\Payment;
use App\Models\Refund;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminReportsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'can:admin dashboard access']);
    }

    /**
     * Display the main reports dashboard
     */
    public function index()
    {
        $data = [
            'totalUsers' => User::count(),
            'activeSubscriptions' => User::whereHas('subscriptions', function($query) {
                $query->where('stripe_status', 'active');
            })->count(),
            'totalRevenue' => Payment::where('status', 'succeeded')->sum('amount') / 100,
            'totalRefunds' => Refund::where('status', 'succeeded')->sum('amount') / 100,
            'monthlyRevenue' => $this->getMonthlyRevenue(),
            'subscriptionsByProduct' => $this->getSubscriptionsByProduct(),
            'userGrowth' => $this->getUserGrowth(),
            'revenueGrowth' => $this->getRevenueGrowth(),
        ];

        return view('admin.reports.index', compact('data'));
    }

    /**
     * Display subscription reports
     */
    public function subscriptions()
    {
        $data = [
            'subscriptionsByStatus' => $this->getSubscriptionsByStatus(),
            'subscriptionsByProduct' => $this->getSubscriptionsByProduct(),
            'churnRate' => $this->getChurnRate(),
            'averageLifetime' => $this->getAverageSubscriptionLifetime(),
            'monthlySubscriptions' => $this->getMonthlySubscriptions(),
        ];

        return view('admin.reports.subscriptions', compact('data'));
    }

    /**
     * Display revenue reports
     */
    public function revenue()
    {
        $data = [
            'monthlyRevenue' => $this->getMonthlyRevenue(),
            'revenueByProduct' => $this->getRevenueByProduct(),
            'averageRevenuePerUser' => $this->getAverageRevenuePerUser(),
            'refundRate' => $this->getRefundRate(),
            'revenueGrowth' => $this->getRevenueGrowth(),
        ];

        return view('admin.reports.revenue', compact('data'));
    }

    /**
     * Display user reports
     */
    public function users()
    {
        $data = [
            'userGrowth' => $this->getUserGrowth(),
            'usersBySubscriptionStatus' => $this->getUsersBySubscriptionStatus(),
            'topUsers' => $this->getTopUsers(),
            'userRetention' => $this->getUserRetention(),
        ];

        return view('admin.reports.users', compact('data'));
    }

    /**
     * Get monthly revenue data for the last 12 months
     */
    private function getMonthlyRevenue()
    {
        return Payment::where('status', 'succeeded')
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, SUM(amount) as total')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'month' => Carbon::createFromDate($item->year, $item->month, 1)->format('M Y'),
                    'total' => $item->total / 100,
                ];
            });
    }

    /**
     * Get subscriptions grouped by product
     */
    private function getSubscriptionsByProduct()
    {
        return DB::table('subscriptions')
            ->join('subscription_products', 'subscriptions.subscription_product_id', '=', 'subscription_products.id')
            ->select('subscription_products.name', DB::raw('count(*) as count'))
            ->where('subscriptions.stripe_status', 'active')
            ->groupBy('subscription_products.name')
            ->get();
    }

    /**
     * Get user growth data for the last 12 months
     */
    private function getUserGrowth()
    {
        return User::where('created_at', '>=', Carbon::now()->subMonths(12))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, COUNT(*) as count')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'month' => Carbon::createFromDate($item->year, $item->month, 1)->format('M Y'),
                    'count' => $item->count,
                ];
            });
    }

    /**
     * Get revenue growth percentage
     */
    private function getRevenueGrowth()
    {
        $currentMonth = Payment::where('status', 'succeeded')
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->sum('amount');

        $previousMonth = Payment::where('status', 'succeeded')
            ->whereMonth('created_at', Carbon::now()->subMonth()->month)
            ->whereYear('created_at', Carbon::now()->subMonth()->year)
            ->sum('amount');

        if ($previousMonth == 0) {
            return $currentMonth > 0 ? 100 : 0;
        }

        return round((($currentMonth - $previousMonth) / $previousMonth) * 100, 2);
    }

    /**
     * Get subscriptions grouped by status
     */
    private function getSubscriptionsByStatus()
    {
        return DB::table('subscriptions')
            ->select('stripe_status', DB::raw('count(*) as count'))
            ->groupBy('stripe_status')
            ->get();
    }

    /**
     * Get churn rate
     */
    private function getChurnRate()
    {
        $totalSubscriptions = DB::table('subscriptions')->count();
        $cancelledSubscriptions = DB::table('subscriptions')
            ->where('stripe_status', 'canceled')
            ->count();

        return $totalSubscriptions > 0 ? round(($cancelledSubscriptions / $totalSubscriptions) * 100, 2) : 0;
    }

    /**
     * Get average subscription lifetime in days
     */
    private function getAverageSubscriptionLifetime()
    {
        $cancelledSubscriptions = DB::table('subscriptions')
            ->where('stripe_status', 'canceled')
            ->whereNotNull('ends_at')
            ->selectRaw('AVG(DATEDIFF(ends_at, created_at)) as avg_lifetime')
            ->first();

        return round($cancelledSubscriptions->avg_lifetime ?? 0, 0);
    }

    /**
     * Get monthly subscription data
     */
    private function getMonthlySubscriptions()
    {
        return DB::table('subscriptions')
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, COUNT(*) as count')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'month' => Carbon::createFromDate($item->year, $item->month, 1)->format('M Y'),
                    'count' => $item->count,
                ];
            });
    }

    /**
     * Get revenue by product
     */
    private function getRevenueByProduct()
    {
        return DB::table('payments')
            ->join('subscriptions', 'payments.subscription_id', '=', 'subscriptions.id')
            ->join('subscription_products', 'subscriptions.subscription_product_id', '=', 'subscription_products.id')
            ->select('subscription_products.name', DB::raw('SUM(payments.amount) as total'))
            ->where('payments.status', 'succeeded')
            ->groupBy('subscription_products.name')
            ->get()
            ->map(function ($item) {
                return [
                    'name' => $item->name,
                    'total' => $item->total / 100,
                ];
            });
    }

    /**
     * Get average revenue per user
     */
    private function getAverageRevenuePerUser()
    {
        $totalRevenue = Payment::where('status', 'succeeded')->sum('amount') / 100;
        $totalUsers = User::whereHas('subscriptions')->count();

        return $totalUsers > 0 ? round($totalRevenue / $totalUsers, 2) : 0;
    }

    /**
     * Get refund rate
     */
    private function getRefundRate()
    {
        $totalPayments = Payment::where('status', 'succeeded')->sum('amount');
        $totalRefunds = Refund::where('status', 'succeeded')->sum('amount');

        return $totalPayments > 0 ? round(($totalRefunds / $totalPayments) * 100, 2) : 0;
    }

    /**
     * Get users by subscription status
     */
    private function getUsersBySubscriptionStatus()
    {
        return [
            'active' => User::whereHas('subscriptions', function($query) {
                $query->where('stripe_status', 'active');
            })->count(),
            'canceled' => User::whereHas('subscriptions', function($query) {
                $query->where('stripe_status', 'canceled');
            })->count(),
            'no_subscription' => User::whereDoesntHave('subscriptions')->count(),
        ];
    }

    /**
     * Get top users by revenue
     */
    private function getTopUsers()
    {
        return User::select('users.*', DB::raw('SUM(payments.amount) as total_revenue'))
            ->join('subscriptions', 'users.id', '=', 'subscriptions.user_id')
            ->join('payments', 'subscriptions.id', '=', 'payments.subscription_id')
            ->where('payments.status', 'succeeded')
            ->groupBy('users.id')
            ->orderBy('total_revenue', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($user) {
                $user->total_revenue = $user->total_revenue / 100;
                return $user;
            });
    }

    /**
     * Get user retention rate
     */
    private function getUserRetention()
    {
        $totalUsers = User::count();
        $activeUsers = User::whereHas('subscriptions', function($query) {
            $query->where('stripe_status', 'active');
        })->count();

        return $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100, 2) : 0;
    }
}
