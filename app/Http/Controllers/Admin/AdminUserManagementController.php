<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\Permission\Models\Role;

class AdminUserManagementController extends Controller
{
    /**
     * Display a listing of users with subscription information
     */
    public function index(Request $request)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'User Management',
                'url' => route('admin.users.index'),
                'active' => true
            ],
        ];

        $q = $request->get('q');
        $perPage = $request->get('per_page', 10);
        $status = $request->get('status');

        $users = QueryBuilder::for(User::class)
            ->allowedFilters(['name', 'email'])
            ->with(['activeSubscription.product', 'roles'])
            ->when($q, function ($query) use ($q) {
                $query->where(function ($subQuery) use ($q) {
                    $subQuery->where('name', 'like', "%{$q}%")
                        ->orWhere('email', 'like', "%{$q}%");
                });
            })
            ->when($status, function ($query) use ($status) {
                if ($status === 'locked') {
                    $query->where('account_locked', true);
                } elseif ($status === 'subscribed') {
                    $query->whereHas('activeSubscription');
                } elseif ($status === 'unsubscribed') {
                    $query->whereDoesntHave('activeSubscription');
                }
            })
            ->latest()
            ->paginate($perPage);

        return view('admin.users.index', [
            'pageTitle' => 'User Management',
            'breadcrumbItems' => $breadcrumbsItems,
            'users' => $users,
            'filters' => [
                'q' => $q,
                'status' => $status,
                'per_page' => $perPage
            ]
        ]);
    }

    /**
     * Display the specified user with detailed information
     */
    public function show(User $user)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'User Management',
                'url' => route('admin.users.index'),
                'active' => false
            ],
            [
                'name' => $user->name,
                'url' => route('admin.users.show', $user),
                'active' => true
            ],
        ];

        $user->load([
            'subscriptions.product',
            'payments' => function ($query) {
                $query->latest()->take(10);
            },
            'invoices' => function ($query) {
                $query->latest()->take(5);
            },
            'roles'
        ]);

        return view('admin.users.show', [
            'pageTitle' => 'User Details - ' . $user->name,
            'breadcrumbItems' => $breadcrumbsItems,
            'user' => $user
        ]);
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        $breadcrumbsItems = [
            [
                'name' => 'Admin',
                'url' => route('admin.dashboard'),
                'active' => false
            ],
            [
                'name' => 'User Management',
                'url' => route('admin.users.index'),
                'active' => false
            ],
            [
                'name' => 'Edit ' . $user->name,
                'url' => route('admin.users.edit', $user),
                'active' => true
            ],
        ];

        $roles = Role::with(['permissions'])->get();

        return view('admin.users.edit', [
            'pageTitle' => 'Edit User - ' . $user->name,
            'breadcrumbItems' => $breadcrumbsItems,
            'user' => $user,
            'roles' => $roles
        ]);
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'roles' => 'array',
            'roles.*' => 'exists:roles,name'
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        if ($request->has('roles')) {
            $user->syncRoles($request->roles);
        }

        return redirect()->route('admin.users.show', $user)
            ->with('message', 'User updated successfully.');
    }

    /**
     * Update user account status (lock/unlock)
     */
    public function updateAccountStatus(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'action' => 'required|in:lock,unlock',
            'reason' => 'required_if:action,lock|string|max:500'
        ]);

        if ($request->action === 'lock') {
            $user->lockAccount($request->reason);
            $message = 'User account has been locked.';
        } else {
            $user->unlockAccount();
            $message = 'User account has been unlocked.';
        }

        return redirect()->route('admin.users.show', $user)
            ->with('message', $message);
    }

    /**
     * Get subscription analytics for a user
     */
    public function subscriptionAnalytics(User $user)
    {
        $subscriptions = $user->subscriptions()
            ->with('product')
            ->orderBy('created_at', 'desc')
            ->get();

        $totalSpent = $user->payments()
            ->where('status', 'succeeded')
            ->sum('amount') / 100; // Convert from cents

        $analytics = [
            'total_subscriptions' => $subscriptions->count(),
            'active_subscriptions' => $subscriptions->where('status', 'active')->count(),
            'total_spent' => $totalSpent,
            'subscription_history' => $subscriptions
        ];

        return response()->json($analytics);
    }

    /**
     * Get payment history for a user
     */
    public function paymentHistory(User $user)
    {
        $payments = $user->payments()
            ->with('subscription.product')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.users.payment-history', [
            'user' => $user,
            'payments' => $payments
        ]);
    }
}
