<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\PaymentMethod\StorePaymentMethodRequest;
use App\Http\Requests\Api\PaymentMethod\UpdatePaymentMethodRequest;
use App\Models\PaymentMethod;
use App\Services\StripeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class PaymentMethodController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
        $this->middleware(['auth:sanctum', 'check.account.locked']);
        $this->authorizeResource(PaymentMethod::class, 'payment_method');
    }

    /**
     * Display a listing of user's payment methods.
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();

        $paymentMethods = $user->paymentMethods()
                              ->orderBy('is_default', 'desc')
                              ->orderBy('created_at', 'desc')
                              ->get();

        return $this->responseWithSuccess(
            'Payment methods retrieved successfully',
            $paymentMethods
        );
    }

    /**
     * Get Stripe configuration for frontend.
     */
    public function getStripeConfig(): JsonResponse
    {
        return $this->responseWithSuccess(
            'Stripe configuration retrieved successfully',
            [
                'publishable_key' => config('services.stripe.key'),
            ]
        );
    }

    /**
     * Create a setup intent for adding a new payment method.
     */
    public function createSetupIntent(): JsonResponse
    {
        $user = Auth::user();

        try {
            // Create or get Stripe customer
            $customer = $this->stripeService->createOrGetCustomer($user);

            // Create setup intent
            $setupIntent = $this->stripeService->createSetupIntent($customer->id);

            return $this->responseWithSuccess(
                'Setup intent created successfully',
                [
                    'client_secret' => $setupIntent->client_secret,
                    'setup_intent_id' => $setupIntent->id,
                ]
            );
        } catch (\Exception $e) {
            Log::error('Failed to create setup intent', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->responseWithError(
                'Failed to create setup intent',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Store a newly created payment method.
     */
    public function store(StorePaymentMethodRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $user = Auth::user();

        try {
            DB::beginTransaction();

            // Create or get Stripe customer
            $customer = $this->stripeService->createOrGetCustomer($user);

            // Attach payment method to customer in Stripe
            $stripePaymentMethod = $this->stripeService->attachPaymentMethodToCustomer(
                $validated['stripe_payment_method_id'],
                $customer->id
            );

            // Retrieve payment method details from Stripe
            $paymentMethodDetails = $this->stripeService->getPaymentMethod($validated['stripe_payment_method_id']);

            // If this should be the default, unset other defaults
            if ($validated['is_default'] ?? false) {
                $user->paymentMethods()->update(['is_default' => false]);
                
                // Set as default in Stripe
                $this->stripeService->setDefaultPaymentMethod($customer->id, $validated['stripe_payment_method_id']);
            }

            // Create local payment method record
            $paymentMethod = PaymentMethod::create([
                'user_id' => $user->id,
                'stripe_payment_method_id' => $validated['stripe_payment_method_id'],
                'type' => $paymentMethodDetails->type,
                'is_default' => $validated['is_default'] ?? false,
                'card_brand' => $paymentMethodDetails->card->brand ?? null,
                'card_last_four' => $paymentMethodDetails->card->last4 ?? null,
                'card_exp_month' => $paymentMethodDetails->card->exp_month ?? null,
                'card_exp_year' => $paymentMethodDetails->card->exp_year ?? null,
                'metadata' => [
                    'stripe_customer_id' => $customer->id,
                ],
            ]);

            DB::commit();

            return $this->responseWithSuccess(
                'Payment method added successfully',
                $paymentMethod,
                Response::HTTP_CREATED
            );

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to store payment method', [
                'user_id' => $user->id,
                'stripe_payment_method_id' => $validated['stripe_payment_method_id'],
                'error' => $e->getMessage()
            ]);

            return $this->responseWithError(
                'Failed to add payment method: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Display the specified payment method.
     */
    public function show(PaymentMethod $paymentMethod): JsonResponse
    {
        // Authorization is handled by the policy
        return $this->responseWithSuccess(
            'Payment method retrieved successfully',
            $paymentMethod
        );
    }

    /**
     * Update the specified payment method.
     */
    public function update(UpdatePaymentMethodRequest $request, PaymentMethod $paymentMethod): JsonResponse
    {
        $validated = $request->validated();
        $user = Auth::user();

        try {
            DB::beginTransaction();

            // If setting as default, unset other defaults and update Stripe
            if (isset($validated['is_default']) && $validated['is_default']) {
                $user->paymentMethods()->where('id', '!=', $paymentMethod->id)->update(['is_default' => false]);
                
                // Set as default in Stripe
                $customer = $this->stripeService->createOrGetCustomer($user);
                $this->stripeService->setDefaultPaymentMethod($customer->id, $paymentMethod->stripe_payment_method_id);
            }

            $paymentMethod->update($validated);

            DB::commit();

            return $this->responseWithSuccess(
                'Payment method updated successfully',
                $paymentMethod
            );

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to update payment method', [
                'payment_method_id' => $paymentMethod->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return $this->responseWithError(
                'Failed to update payment method: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Remove the specified payment method.
     */
    public function destroy(PaymentMethod $paymentMethod): JsonResponse
    {
        $user = Auth::user();

        try {
            DB::beginTransaction();

            // Detach from Stripe
            $this->stripeService->detachPaymentMethod($paymentMethod->stripe_payment_method_id);

            // If this was the default, set another one as default
            if ($paymentMethod->is_default) {
                $newDefault = $user->paymentMethods()
                                  ->where('id', '!=', $paymentMethod->id)
                                  ->first();
                
                if ($newDefault) {
                    $newDefault->update(['is_default' => true]);
                    
                    $customer = $this->stripeService->createOrGetCustomer($user);
                    $this->stripeService->setDefaultPaymentMethod($customer->id, $newDefault->stripe_payment_method_id);
                }
            }

            $paymentMethod->delete();

            DB::commit();

            return $this->responseWithSuccess(
                'Payment method removed successfully'
            );

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to remove payment method', [
                'payment_method_id' => $paymentMethod->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return $this->responseWithError(
                'Failed to remove payment method: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Set a payment method as default.
     */
    public function setDefault(PaymentMethod $paymentMethod): JsonResponse
    {
        $user = Auth::user();

        // Check authorization
        if ($paymentMethod->user_id !== $user->id) {
            return $this->responseWithError(
                'Unauthorized',
                Response::HTTP_FORBIDDEN
            );
        }

        try {
            DB::beginTransaction();

            // Unset other defaults
            $user->paymentMethods()->update(['is_default' => false]);

            // Set this one as default
            $paymentMethod->update(['is_default' => true]);

            // Update in Stripe
            $customer = $this->stripeService->createOrGetCustomer($user);
            $this->stripeService->setDefaultPaymentMethod($customer->id, $paymentMethod->stripe_payment_method_id);

            DB::commit();

            return $this->responseWithSuccess(
                'Default payment method updated successfully',
                $paymentMethod
            );

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to set default payment method', [
                'payment_method_id' => $paymentMethod->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return $this->responseWithError(
                'Failed to set default payment method: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
