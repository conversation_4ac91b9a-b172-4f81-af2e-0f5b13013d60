<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\UserSubscription;
use App\Models\User;
use App\Services\StripeService;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use Symfony\Component\HttpFoundation\Response;

class StripeWebhookController extends Controller
{
    protected StripeService $stripeService;
    protected NotificationService $notificationService;

    public function __construct(StripeService $stripeService, NotificationService $notificationService)
    {
        $this->stripeService = $stripeService;
        $this->notificationService = $notificationService;
    }

    /**
     * Handle Stripe webhook events
     */
    public function handleWebhook(Request $request): JsonResponse
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpointSecret = config('services.stripe.webhook.secret');

        try {
            // Verify webhook signature
            $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
        } catch (\UnexpectedValueException $e) {
            Log::error('Invalid payload in Stripe webhook', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Invalid payload'], Response::HTTP_BAD_REQUEST);
        } catch (SignatureVerificationException $e) {
            Log::error('Invalid signature in Stripe webhook', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Invalid signature'], Response::HTTP_BAD_REQUEST);
        }

        Log::info('Stripe webhook received', [
            'event_type' => $event->type,
            'event_id' => $event->id
        ]);

        try {
            // Handle the event
            switch ($event->type) {
                case 'payment_intent.succeeded':
                    $this->handlePaymentIntentSucceeded($event->data->object);
                    break;

                case 'payment_intent.payment_failed':
                    $this->handlePaymentIntentFailed($event->data->object);
                    break;

                case 'payment_intent.requires_action':
                    $this->handlePaymentIntentRequiresAction($event->data->object);
                    break;

                case 'invoice.payment_succeeded':
                    $this->handleInvoicePaymentSucceeded($event->data->object);
                    break;

                case 'invoice.payment_failed':
                    $this->handleInvoicePaymentFailed($event->data->object);
                    break;

                case 'customer.subscription.created':
                    $this->handleSubscriptionCreated($event->data->object);
                    break;

                case 'customer.subscription.updated':
                    $this->handleSubscriptionUpdated($event->data->object);
                    break;

                case 'customer.subscription.deleted':
                    $this->handleSubscriptionDeleted($event->data->object);
                    break;

                default:
                    Log::info('Unhandled Stripe webhook event', ['event_type' => $event->type]);
            }

            return response()->json(['status' => 'success']);

        } catch (\Exception $e) {
            Log::error('Error processing Stripe webhook', [
                'event_type' => $event->type,
                'event_id' => $event->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Webhook processing failed'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Handle successful payment intent
     */
    protected function handlePaymentIntentSucceeded($paymentIntent): void
    {
        $payment = Payment::where('stripe_payment_intent_id', $paymentIntent->id)->first();

        if ($payment) {
            $payment->update([
                'status' => 'succeeded',
                'paid_at' => now(),
                'payment_method_type' => $paymentIntent->charges->data[0]->payment_method_details->type ?? null,
                'requires_3d_secure' => false,
                '3d_secure_url' => null,
            ]);

            // Send payment success notification
            $this->notificationService->sendPaymentSucceededNotification($payment->user, $payment);

            Log::info('Payment succeeded', [
                'payment_id' => $payment->id,
                'stripe_payment_intent_id' => $paymentIntent->id
            ]);
        }
    }

    /**
     * Handle failed payment intent
     */
    protected function handlePaymentIntentFailed($paymentIntent): void
    {
        $payment = Payment::where('stripe_payment_intent_id', $paymentIntent->id)->first();

        if ($payment) {
            $payment->update([
                'status' => 'failed',
                'failure_code' => $paymentIntent->last_payment_error->code ?? null,
                'failure_message' => $paymentIntent->last_payment_error->message ?? 'Payment failed',
                'requires_3d_secure' => false,
                '3d_secure_url' => null,
            ]);

            // Send payment failed notification
            $failureReason = $payment->failure_message ?? 'Payment failed';
            $this->notificationService->sendPaymentFailedNotification($payment->user, $payment, $failureReason);

            Log::warning('Payment failed', [
                'payment_id' => $payment->id,
                'stripe_payment_intent_id' => $paymentIntent->id,
                'failure_code' => $payment->failure_code,
                'failure_message' => $payment->failure_message
            ]);
        }
    }

    /**
     * Handle payment intent requiring action (3D Secure)
     */
    protected function handlePaymentIntentRequiresAction($paymentIntent): void
    {
        $payment = Payment::where('stripe_payment_intent_id', $paymentIntent->id)->first();

        if ($payment) {
            $payment->update([
                'status' => 'requires_action',
                'requires_3d_secure' => true,
                '3d_secure_url' => $paymentIntent->next_action->redirect_to_url->url ?? null,
            ]);

            // Send payment requires action notification
            $actionUrl = $payment->{'3d_secure_url'} ?? url('/dashboard/payments/' . $payment->id);
            $this->notificationService->sendPaymentRequiresActionNotification($payment->user, $payment, $actionUrl);

            Log::info('Payment requires 3D Secure authentication', [
                'payment_id' => $payment->id,
                'stripe_payment_intent_id' => $paymentIntent->id
            ]);
        }
    }

    /**
     * Handle successful invoice payment
     */
    protected function handleInvoicePaymentSucceeded($invoice): void
    {
        $subscription = UserSubscription::where('stripe_subscription_id', $invoice->subscription)->first();

        if ($subscription) {
            $subscription->update([
                'status' => 'active',
                'current_period_start' => \Carbon\Carbon::createFromTimestamp($invoice->period_start),
                'current_period_end' => \Carbon\Carbon::createFromTimestamp($invoice->period_end),
            ]);

            Log::info('Subscription invoice payment succeeded', [
                'subscription_id' => $subscription->id,
                'stripe_subscription_id' => $invoice->subscription
            ]);
        }
    }

    /**
     * Handle failed invoice payment
     */
    protected function handleInvoicePaymentFailed($invoice): void
    {
        $subscription = UserSubscription::where('stripe_subscription_id', $invoice->subscription)->first();

        if ($subscription) {
            $subscription->update([
                'status' => 'past_due',
            ]);

            Log::warning('Subscription invoice payment failed', [
                'subscription_id' => $subscription->id,
                'stripe_subscription_id' => $invoice->subscription
            ]);
        }
    }

    /**
     * Handle subscription created
     */
    protected function handleSubscriptionCreated($subscription): void
    {
        $userSubscription = UserSubscription::where('stripe_subscription_id', $subscription->id)->first();

        if ($userSubscription) {
            $userSubscription->update([
                'status' => $subscription->status,
                'current_period_start' => \Carbon\Carbon::createFromTimestamp($subscription->current_period_start),
                'current_period_end' => \Carbon\Carbon::createFromTimestamp($subscription->current_period_end),
            ]);

            Log::info('Subscription created webhook processed', [
                'subscription_id' => $userSubscription->id,
                'stripe_subscription_id' => $subscription->id
            ]);
        }
    }

    /**
     * Handle subscription updated
     */
    protected function handleSubscriptionUpdated($subscription): void
    {
        $userSubscription = UserSubscription::where('stripe_subscription_id', $subscription->id)->first();

        if ($userSubscription) {
            $userSubscription->update([
                'status' => $subscription->status,
                'current_period_start' => \Carbon\Carbon::createFromTimestamp($subscription->current_period_start),
                'current_period_end' => \Carbon\Carbon::createFromTimestamp($subscription->current_period_end),
            ]);

            Log::info('Subscription updated webhook processed', [
                'subscription_id' => $userSubscription->id,
                'stripe_subscription_id' => $subscription->id,
                'new_status' => $subscription->status
            ]);
        }
    }

    /**
     * Handle subscription deleted
     */
    protected function handleSubscriptionDeleted($subscription): void
    {
        $userSubscription = UserSubscription::where('stripe_subscription_id', $subscription->id)->first();

        if ($userSubscription) {
            $userSubscription->update([
                'status' => 'canceled',
                'canceled_at' => now(),
            ]);

            Log::info('Subscription deleted webhook processed', [
                'subscription_id' => $userSubscription->id,
                'stripe_subscription_id' => $subscription->id
            ]);
        }
    }
}
