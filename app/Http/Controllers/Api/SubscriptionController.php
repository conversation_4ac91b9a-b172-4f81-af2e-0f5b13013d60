<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionProduct;
use App\Models\UserSubscription;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SubscriptionController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
        $this->middleware(['auth:sanctum', 'check.account.locked']);
    }

    /**
     * Display user's subscriptions.
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();
        $subscriptions = $user->subscriptions()
            ->with('subscriptionProduct')
            ->orderBy('created_at', 'desc')
            ->get();

        return $this->responseWithSuccess(
            'User subscriptions retrieved successfully',
            $subscriptions
        );
    }

    /**
     * Subscribe to a product.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'subscription_product_id' => 'required|exists:subscription_products,id',
            'payment_method_id' => 'required|string',
        ]);

        $user = Auth::user();
        $product = SubscriptionProduct::findOrFail($validated['subscription_product_id']);

        try {
            // Check if user already has an active subscription
            if ($user->hasActiveSubscription()) {
                return $this->responseWithError(
                    'User already has an active subscription',
                    Response::HTTP_CONFLICT
                );
            }

            // Create or get Stripe customer
            $customer = $this->stripeService->createOrGetCustomer($user);

            // Attach payment method to customer
            $this->stripeService->attachPaymentMethodToCustomer(
                $validated['payment_method_id'],
                $customer->id
            );

            // Set as default payment method
            $this->stripeService->setDefaultPaymentMethod(
                $customer->id,
                $validated['payment_method_id']
            );

            // Create subscription in Stripe
            $stripeSubscription = $this->stripeService->createSubscription(
                $customer->id,
                $product->stripe_price_id,
                [
                    'default_payment_method' => $validated['payment_method_id'],
                    'expand' => ['latest_invoice.payment_intent'],
                ]
            );

            // Create local subscription record
            $subscription = UserSubscription::create([
                'user_id' => $user->id,
                'subscription_product_id' => $product->id,
                'stripe_subscription_id' => $stripeSubscription->id,
                'status' => $stripeSubscription->status,
                'current_period_start' => \Carbon\Carbon::createFromTimestamp($stripeSubscription->current_period_start),
                'current_period_end' => \Carbon\Carbon::createFromTimestamp($stripeSubscription->current_period_end),
                'amount' => $product->price,
                'currency' => 'usd',
            ]);

            // If payment requires action (3D Secure)
            if ($stripeSubscription->latest_invoice->payment_intent->status === 'requires_action') {
                return $this->responseWithSuccess(
                    'Subscription created but requires payment confirmation',
                    [
                        'subscription' => $subscription->load('subscriptionProduct'),
                        'requires_action' => true,
                        'payment_intent' => [
                            'id' => $stripeSubscription->latest_invoice->payment_intent->id,
                            'client_secret' => $stripeSubscription->latest_invoice->payment_intent->client_secret,
                        ]
                    ]
                );
            }

            return $this->responseWithSuccess(
                'Subscription created successfully',
                $subscription->load('subscriptionProduct'),
                Response::HTTP_CREATED
            );

        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to create subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Display the specified subscription.
     */
    public function show(UserSubscription $subscription): JsonResponse
    {
        // Ensure user can only view their own subscriptions
        if ($subscription->user_id !== Auth::id()) {
            return $this->responseWithError(
                'Unauthorized',
                Response::HTTP_FORBIDDEN
            );
        }

        $subscription->load(['subscriptionProduct', 'payments', 'invoices']);

        return $this->responseWithSuccess(
            'Subscription retrieved successfully',
            $subscription
        );
    }

    /**
     * Update subscription (upgrade/downgrade).
     */
    public function update(Request $request, UserSubscription $subscription): JsonResponse
    {
        // Ensure user can only update their own subscriptions
        if ($subscription->user_id !== Auth::id()) {
            return $this->responseWithError(
                'Unauthorized',
                Response::HTTP_FORBIDDEN
            );
        }

        $validated = $request->validate([
            'subscription_product_id' => 'required|exists:subscription_products,id',
        ]);

        $newProduct = SubscriptionProduct::findOrFail($validated['subscription_product_id']);

        try {
            // Update subscription in Stripe
            $stripeSubscription = $this->stripeService->updateSubscription(
                $subscription->stripe_subscription_id,
                $newProduct->stripe_price_id
            );

            // Update local subscription
            $subscription->update([
                'subscription_product_id' => $newProduct->id,
                'status' => $stripeSubscription->status,
                'amount' => $newProduct->price,
            ]);

            return $this->responseWithSuccess(
                'Subscription updated successfully',
                $subscription->fresh()->load('subscriptionProduct')
            );

        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to update subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Cancel subscription.
     */
    public function destroy(UserSubscription $subscription): JsonResponse
    {
        // Ensure user can only cancel their own subscriptions
        if ($subscription->user_id !== Auth::id()) {
            return $this->responseWithError(
                'Unauthorized',
                Response::HTTP_FORBIDDEN
            );
        }

        try {
            // Cancel subscription in Stripe
            $stripeSubscription = $this->stripeService->cancelSubscription(
                $subscription->stripe_subscription_id
            );

            // Update local subscription
            $subscription->update([
                'status' => 'canceled',
                'canceled_at' => now(),
                'ends_at' => \Carbon\Carbon::createFromTimestamp($stripeSubscription->current_period_end),
            ]);

            return $this->responseWithSuccess(
                'Subscription canceled successfully',
                $subscription->fresh()
            );

        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to cancel subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
