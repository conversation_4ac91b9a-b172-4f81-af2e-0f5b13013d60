<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionProduct;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Symfony\Component\HttpFoundation\Response;

class SubscriptionProductController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Display a listing of subscription products.
     */
    public function index(Request $request): JsonResponse
    {
        $query = SubscriptionProduct::query();

        // Filter by active status if requested
        if ($request->has('active')) {
            $query->where('is_active', $request->boolean('active'));
        }

        // Apply ordering
        $query->ordered();

        $products = $query->get();

        return $this->responseWithSuccess(
            'Subscription products retrieved successfully',
            $products
        );
    }

    /**
     * Store a newly created subscription product.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => ['required', Rule::in(['monthly', 'yearly', 'quarterly'])],
            'features' => 'nullable|array',
            'limits' => 'nullable|array',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        try {
            // Create product in Stripe first
            $stripeProduct = $this->stripeService->createProduct([
                'name' => $validated['name'],
                'description' => $validated['description'] ?? null,
            ]);

            // Create price in Stripe
            $stripePrice = $this->stripeService->createPrice([
                'product' => $stripeProduct->id,
                'unit_amount' => $validated['price'] * 100, // Convert to cents
                'currency' => 'usd',
                'recurring' => [
                    'interval' => $validated['billing_cycle'] === 'yearly' ? 'year' :
                                 ($validated['billing_cycle'] === 'quarterly' ? 'month' : 'month'),
                    'interval_count' => $validated['billing_cycle'] === 'quarterly' ? 3 : 1,
                ],
            ]);

            // Create local product
            $product = SubscriptionProduct::create($validated + [
                'stripe_product_id' => $stripeProduct->id,
                'stripe_price_id' => $stripePrice->id,
                'sort_order' => $validated['sort_order'] ?? 0,
                'is_active' => $validated['is_active'] ?? true,
            ]);

            return $this->responseWithSuccess(
                'Subscription product created successfully',
                $product,
                Response::HTTP_CREATED
            );
        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to create subscription product: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Display the specified subscription product.
     */
    public function show(SubscriptionProduct $subscriptionProduct): JsonResponse
    {
        return $this->responseWithSuccess(
            'Subscription product retrieved successfully',
            $subscriptionProduct
        );
    }

    /**
     * Update the specified subscription product.
     */
    public function update(Request $request, SubscriptionProduct $subscriptionProduct): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'sometimes|required|numeric|min:0',
            'billing_cycle' => ['sometimes', 'required', Rule::in(['monthly', 'yearly', 'quarterly'])],
            'features' => 'nullable|array',
            'limits' => 'nullable|array',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        try {
            // Update Stripe product if name or description changed
            if (isset($validated['name']) || isset($validated['description'])) {
                $this->stripeService->updateProduct($subscriptionProduct->stripe_product_id, [
                    'name' => $validated['name'] ?? $subscriptionProduct->name,
                    'description' => $validated['description'] ?? $subscriptionProduct->description,
                ]);
            }

            // If price or billing cycle changed, create new price in Stripe
            if (isset($validated['price']) || isset($validated['billing_cycle'])) {
                $stripePrice = $this->stripeService->createPrice([
                    'product' => $subscriptionProduct->stripe_product_id,
                    'unit_amount' => ($validated['price'] ?? $subscriptionProduct->price) * 100,
                    'currency' => 'usd',
                    'recurring' => [
                        'interval' => ($validated['billing_cycle'] ?? $subscriptionProduct->billing_cycle) === 'yearly' ? 'year' : 'month',
                        'interval_count' => ($validated['billing_cycle'] ?? $subscriptionProduct->billing_cycle) === 'quarterly' ? 3 : 1,
                    ],
                ]);

                $validated['stripe_price_id'] = $stripePrice->id;
            }

            $subscriptionProduct->update($validated);

            return $this->responseWithSuccess(
                'Subscription product updated successfully',
                $subscriptionProduct->fresh()
            );
        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to update subscription product: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Remove the specified subscription product.
     */
    public function destroy(SubscriptionProduct $subscriptionProduct): JsonResponse
    {
        try {
            // Check if product has active subscriptions
            if ($subscriptionProduct->userSubscriptions()->where('status', 'active')->exists()) {
                return $this->responseWithError(
                    'Cannot delete subscription product with active subscriptions',
                    Response::HTTP_CONFLICT
                );
            }

            // Archive the product in Stripe instead of deleting
            $this->stripeService->updateProduct($subscriptionProduct->stripe_product_id, [
                'active' => false,
            ]);

            // Soft delete or mark as inactive
            $subscriptionProduct->update(['is_active' => false]);

            return $this->responseWithSuccess(
                'Subscription product deactivated successfully'
            );
        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to delete subscription product: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
