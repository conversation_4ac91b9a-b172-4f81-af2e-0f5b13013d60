<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'stripe_payment_method_id',
        'type',
        'is_default',
        'card_brand',
        'card_last_four',
        'card_exp_month',
        'card_exp_year',
        'bank_name',
        'bank_last_four',
        'metadata',
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'card_exp_month' => 'integer',
        'card_exp_year' => 'integer',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the payment method
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get default payment methods
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope to get card payment methods
     */
    public function scopeCards($query)
    {
        return $query->where('type', 'card');
    }

    /**
     * Check if payment method is a card
     */
    public function isCard(): bool
    {
        return $this->type === 'card';
    }

    /**
     * Check if card is expired
     */
    public function isExpired(): bool
    {
        if (!$this->isCard()) {
            return false;
        }

        $currentYear = (int) date('Y');
        $currentMonth = (int) date('m');

        return $this->card_exp_year < $currentYear ||
               ($this->card_exp_year == $currentYear && $this->card_exp_month < $currentMonth);
    }

    /**
     * Get display name for payment method
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->isCard()) {
            return ucfirst($this->card_brand) . ' ending in ' . $this->card_last_four;
        }

        if ($this->type === 'bank_account') {
            return ($this->bank_name ?? 'Bank') . ' ending in ' . $this->bank_last_four;
        }

        return ucfirst($this->type);
    }

    /**
     * Get card expiration display
     */
    public function getCardExpirationAttribute(): ?string
    {
        if (!$this->isCard()) {
            return null;
        }

        return str_pad($this->card_exp_month, 2, '0', STR_PAD_LEFT) . '/' . $this->card_exp_year;
    }

    /**
     * Set a payment method as default and unset others
     */
    public function setAsDefault(): void
    {
        // Unset other default payment methods for this user
        static::where('user_id', $this->user_id)
              ->where('id', '!=', $this->id)
              ->update(['is_default' => false]);

        // Set this one as default
        $this->update(['is_default' => true]);
    }
}
