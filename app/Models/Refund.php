<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Refund extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'payment_id',
        'user_subscription_id',
        'stripe_refund_id',
        'amount',
        'currency',
        'status',
        'reason',
        'description',
        'metadata',
        'processed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'metadata' => 'array',
        'processed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the refund
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the payment that was refunded
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Get the subscription associated with this refund
     */
    public function userSubscription(): BelongsTo
    {
        return $this->belongsTo(UserSubscription::class);
    }

    /**
     * Scope to get successful refunds
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'succeeded');
    }

    /**
     * Scope to get pending refunds
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Check if refund was successful
     */
    public function isSuccessful(): bool
    {
        return $this->status === 'succeeded';
    }

    /**
     * Check if refund is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Get formatted amount with currency
     */
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get status display name
     */
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'succeeded' => 'Successful',
            'pending' => 'Pending',
            'failed' => 'Failed',
            'canceled' => 'Canceled',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get reason display name
     */
    public function getReasonDisplayAttribute(): string
    {
        return match($this->reason) {
            'duplicate' => 'Duplicate Payment',
            'fraudulent' => 'Fraudulent',
            'requested_by_customer' => 'Customer Request',
            'expired_uncaptured_charge' => 'Expired Charge',
            default => ucfirst(str_replace('_', ' ', $this->reason)),
        };
    }
}
