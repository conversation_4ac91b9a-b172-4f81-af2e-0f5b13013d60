<?php

namespace App\Notifications;

use App\Models\Invoice;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class InvoiceGeneratedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected Invoice $invoice;

    /**
     * Create a new notification instance.
     */
    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subscriptionName = $this->invoice->userSubscription?->subscriptionProduct?->name ?? 'Subscription';
        
        return (new MailMessage)
            ->subject('New Invoice Available')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('A new invoice has been generated for your ' . $subscriptionName . '.')
            ->line('Invoice Amount: $' . number_format($this->invoice->amount, 2))
            ->line('Invoice Date: ' . $this->invoice->created_at->format('M j, Y'))
            ->when($this->invoice->due_date, function ($message) {
                return $message->line('Due Date: ' . $this->invoice->due_date->format('M j, Y'));
            })
            ->line('You can view and download your invoice from your dashboard.')
            ->action('View Invoice', url('/dashboard/invoices/' . $this->invoice->id))
            ->when($this->invoice->status === 'open', function ($message) {
                return $message->line('Please ensure payment is made by the due date to avoid any service interruption.');
            })
            ->salutation('Best regards, ' . config('app.name') . ' Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'invoice_generated',
            'invoice_id' => $this->invoice->id,
            'amount' => $this->invoice->amount,
            'currency' => $this->invoice->currency,
            'status' => $this->invoice->status,
            'subscription_name' => $this->invoice->userSubscription?->subscriptionProduct?->name,
            'invoice_date' => $this->invoice->created_at->toISOString(),
            'due_date' => $this->invoice->due_date?->toISOString(),
            'action_url' => url('/dashboard/invoices/' . $this->invoice->id),
            'message' => 'New invoice generated for ' . ($this->invoice->userSubscription?->subscriptionProduct?->name ?? 'subscription') . '.',
        ];
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType(object $notifiable): string
    {
        return 'invoice_generated';
    }
}
