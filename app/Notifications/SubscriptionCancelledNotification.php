<?php

namespace App\Notifications;

use App\Models\UserSubscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SubscriptionCancelledNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected UserSubscription $subscription;
    protected string $reason;

    /**
     * Create a new notification instance.
     */
    public function __construct(UserSubscription $subscription, string $reason = '')
    {
        $this->subscription = $subscription;
        $this->reason = $reason;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subscriptionName = $this->subscription->subscriptionProduct?->name ?? 'Subscription';
        
        return (new MailMessage)
            ->subject('Subscription Cancelled')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Your ' . $subscriptionName . ' subscription has been cancelled.')
            ->when($this->reason, function ($message) {
                return $message->line('Reason: ' . $this->reason);
            })
            ->line('Cancellation Date: ' . $this->subscription->cancelled_at?->format('M j, Y'))
            ->when($this->subscription->ends_at, function ($message) {
                return $message->line('Service End Date: ' . $this->subscription->ends_at->format('M j, Y'))
                    ->line('You will continue to have access to your subscription benefits until ' . $this->subscription->ends_at->format('M j, Y') . '.');
            })
            ->line('We\'re sorry to see you go! If you change your mind, you can reactivate your subscription at any time.')
            ->action('Reactivate Subscription', url('/dashboard/subscriptions'))
            ->line('If you have any questions or feedback, please don\'t hesitate to contact our support team.')
            ->salutation('Best regards, ' . config('app.name') . ' Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'subscription_cancelled',
            'subscription_id' => $this->subscription->id,
            'subscription_name' => $this->subscription->subscriptionProduct?->name,
            'reason' => $this->reason,
            'cancelled_at' => $this->subscription->cancelled_at?->toISOString(),
            'ends_at' => $this->subscription->ends_at?->toISOString(),
            'action_url' => url('/dashboard/subscriptions'),
            'message' => 'Your ' . ($this->subscription->subscriptionProduct?->name ?? 'subscription') . ' has been cancelled.',
        ];
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType(object $notifiable): string
    {
        return 'subscription_cancelled';
    }
}
