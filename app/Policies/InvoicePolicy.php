<?php

namespace App\Policies;

use App\Models\Invoice;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class InvoicePolicy
{
    /**
     * Determine whether the user can view any invoices.
     */
    public function viewAny(User $user): bool
    {
        // All authenticated users can view their own invoices
        return true;
    }

    /**
     * Determine whether the user can view the invoice.
     */
    public function view(User $user, Invoice $invoice): bool
    {
        // Users can only view their own invoices
        // Admins can view all invoices
        return $user->id === $invoice->user_id || 
               $user->hasRole(['super-admin', 'admin']) ||
               $user->hasPermissionTo('admin invoice management');
    }

    /**
     * Determine whether the user can create invoices.
     */
    public function create(User $user): bool
    {
        // All authenticated users can generate invoices for their subscriptions
        // Admins can create invoices for any user
        return true;
    }

    /**
     * Determine whether the user can update the invoice.
     */
    public function update(User $user, Invoice $invoice): bool
    {
        // Only admins can update invoices
        return $user->hasRole(['super-admin', 'admin']) ||
               $user->hasPermissionTo('admin invoice management');
    }

    /**
     * Determine whether the user can delete the invoice.
     */
    public function delete(User $user, Invoice $invoice): bool
    {
        // Only super-admin can delete invoices
        return $user->hasRole('super-admin');
    }

    /**
     * Determine whether the user can restore the invoice.
     */
    public function restore(User $user, Invoice $invoice): bool
    {
        // Only super-admin can restore invoices
        return $user->hasRole('super-admin');
    }

    /**
     * Determine whether the user can permanently delete the invoice.
     */
    public function forceDelete(User $user, Invoice $invoice): bool
    {
        // Only super-admin can permanently delete invoices
        return $user->hasRole('super-admin');
    }

    /**
     * Determine whether the user can download the invoice.
     */
    public function download(User $user, Invoice $invoice): bool
    {
        // Users can download their own invoices
        // Admins can download all invoices
        return $user->id === $invoice->user_id || 
               $user->hasRole(['super-admin', 'admin']) ||
               $user->hasPermissionTo('admin invoice management');
    }

    /**
     * Determine whether the user can pay the invoice.
     */
    public function pay(User $user, Invoice $invoice): bool
    {
        // Users can pay their own invoices
        // Admins can pay any invoice
        return $user->id === $invoice->user_id || 
               $user->hasRole(['super-admin', 'admin']) ||
               $user->hasPermissionTo('admin invoice management');
    }
}
