<?php

namespace App\Services;

use App\Models\User;
use Stripe\StripeClient;
use Stripe\Customer;
use Stripe\PaymentMethod;
use Stripe\PaymentIntent;
use Stripe\Subscription;
use Stripe\Price;
use Stripe\Product;
use Stripe\Invoice;
use Stripe\Refund;
use Illuminate\Support\Facades\Log;

class StripeService
{
    protected StripeClient $stripe;

    public function __construct()
    {
        $this->stripe = app('stripe');
    }

    /**
     * Create or retrieve a Stripe customer for a user
     */
    public function createOrGetCustomer(User $user): Customer
    {
        if ($user->stripe_customer_id) {
            try {
                return $this->stripe->customers->retrieve($user->stripe_customer_id);
            } catch (\Exception $e) {
                Log::warning('Failed to retrieve Stripe customer', [
                    'user_id' => $user->id,
                    'stripe_customer_id' => $user->stripe_customer_id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $customer = $this->stripe->customers->create([
            'email' => $user->email,
            'name' => $user->name,
            'metadata' => [
                'user_id' => $user->id,
            ],
        ]);

        $user->update(['stripe_customer_id' => $customer->id]);

        return $customer;
    }

    /**
     * Create a payment intent
     */
    public function createPaymentIntent(int $amount, string $currency = 'usd', array $metadata = []): PaymentIntent
    {
        return $this->stripe->paymentIntents->create([
            'amount' => $amount,
            'currency' => $currency,
            'metadata' => $metadata,
            'automatic_payment_methods' => [
                'enabled' => true,
            ],
        ]);
    }

    /**
     * Create a subscription
     */
    public function createSubscription(string $customerId, string $priceId, array $options = []): Subscription
    {
        $params = array_merge([
            'customer' => $customerId,
            'items' => [
                ['price' => $priceId],
            ],
            'payment_behavior' => 'default_incomplete',
            'payment_settings' => [
                'save_default_payment_method' => 'on_subscription',
            ],
            'expand' => ['latest_invoice.payment_intent'],
        ], $options);

        return $this->stripe->subscriptions->create($params);
    }

    /**
     * Cancel a subscription
     */
    public function cancelSubscription(string $subscriptionId, bool $immediately = false): Subscription
    {
        if ($immediately) {
            return $this->stripe->subscriptions->cancel($subscriptionId);
        }

        return $this->stripe->subscriptions->update($subscriptionId, [
            'cancel_at_period_end' => true,
        ]);
    }

    /**
     * Create a refund
     */
    public function createRefund(string $paymentIntentId, int $amount = null, array $metadata = []): Refund
    {
        $params = [
            'payment_intent' => $paymentIntentId,
            'metadata' => $metadata,
        ];

        if ($amount) {
            $params['amount'] = $amount;
        }

        return $this->stripe->refunds->create($params);
    }

    /**
     * Retrieve an invoice
     */
    public function getInvoice(string $invoiceId): Invoice
    {
        return $this->stripe->invoices->retrieve($invoiceId);
    }

    /**
     * Create a Stripe product
     */
    public function createProduct(array $data)
    {
        // Support both old and new format
        if (isset($data['name']) && is_string($data['name']) && !isset($data['description'])) {
            // Old format: createProduct($name, $description)
            $name = $data['name'];
            $description = $data['description'] ?? null;
            return $this->stripe->products->create([
                'name' => $name,
                'description' => $description,
            ]);
        }

        // New format: createProduct($data)
        return $this->stripe->products->create($data);
    }

    /**
     * Create a Stripe price
     */
    public function createPrice(array $data)
    {
        return $this->stripe->prices->create($data);
    }

    /**
     * Attach payment method to customer
     */
    public function attachPaymentMethod(string $paymentMethodId, string $customerId): PaymentMethod
    {
        return $this->stripe->paymentMethods->attach($paymentMethodId, [
            'customer' => $customerId,
        ]);
    }

    /**
     * Set default payment method for customer
     */
    public function setDefaultPaymentMethod(string $customerId, string $paymentMethodId): Customer
    {
        return $this->stripe->customers->update($customerId, [
            'invoice_settings' => [
                'default_payment_method' => $paymentMethodId,
            ],
        ]);
    }

    /**
     * Get customer's payment methods
     */
    public function getCustomerPaymentMethods(string $customerId, string $type = 'card'): array
    {
        $paymentMethods = $this->stripe->paymentMethods->all([
            'customer' => $customerId,
            'type' => $type,
        ]);

        return $paymentMethods->data;
    }

    /**
     * Calculate prorated refund amount
     */
    public function calculateProratedRefund(Subscription $subscription): int
    {
        $currentPeriodStart = $subscription->current_period_start;
        $currentPeriodEnd = $subscription->current_period_end;
        $now = time();

        $totalDays = ($currentPeriodEnd - $currentPeriodStart) / 86400; // Convert to days
        $usedDays = ($now - $currentPeriodStart) / 86400;
        $remainingDays = $totalDays - $usedDays;

        if ($remainingDays <= 0) {
            return 0;
        }

        $totalAmount = $subscription->items->data[0]->price->unit_amount;
        $refundAmount = ($remainingDays / $totalDays) * $totalAmount;

        return (int) round($refundAmount);
    }

    /**
     * Update a product in Stripe
     */
    public function updateProduct(string $productId, array $data)
    {
        return $this->stripe->products->update($productId, $data);
    }

    /**
     * Get a product from Stripe
     */
    public function getProduct(string $productId)
    {
        return $this->stripe->products->retrieve($productId);
    }

    /**
     * Get a price from Stripe
     */
    public function getPrice(string $priceId)
    {
        return $this->stripe->prices->retrieve($priceId);
    }

    /**
     * Get a subscription from Stripe
     */
    public function getSubscription(string $subscriptionId)
    {
        return $this->stripe->subscriptions->retrieve($subscriptionId);
    }

    /**
     * Update a subscription
     */
    public function updateSubscription(string $subscriptionId, string $newPriceId): Subscription
    {
        $subscription = $this->stripe->subscriptions->retrieve($subscriptionId);

        return $this->stripe->subscriptions->update($subscriptionId, [
            'items' => [
                [
                    'id' => $subscription->items->data[0]->id,
                    'price' => $newPriceId,
                ],
            ],
            'proration_behavior' => 'create_prorations',
        ]);
    }

    /**
     * Cancel subscription immediately
     */
    public function cancelSubscriptionImmediately(string $subscriptionId): Subscription
    {
        return $this->stripe->subscriptions->cancel($subscriptionId);
    }

    /**
     * Pause a subscription
     */
    public function pauseSubscription(string $subscriptionId, string $resumeDate = null): Subscription
    {
        $params = [
            'pause_collection' => [
                'behavior' => 'void',
            ],
        ];

        if ($resumeDate) {
            $params['pause_collection']['resumes_at'] = strtotime($resumeDate);
        }

        return $this->stripe->subscriptions->update($subscriptionId, $params);
    }

    /**
     * Resume a paused subscription
     */
    public function resumeSubscription(string $subscriptionId): Subscription
    {
        return $this->stripe->subscriptions->update($subscriptionId, [
            'pause_collection' => '',
        ]);
    }
}
