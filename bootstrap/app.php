<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->booting(function ($app) {
        // Manually register core service providers that are not being auto-discovered
        $app->register(\Illuminate\Filesystem\FilesystemServiceProvider::class);
        $app->register(\Illuminate\Cache\CacheServiceProvider::class);
        $app->register(\Illuminate\Session\SessionServiceProvider::class);
        $app->register(\Illuminate\View\ViewServiceProvider::class);
        $app->register(\Illuminate\Encryption\EncryptionServiceProvider::class);
        $app->register(\Illuminate\Cookie\CookieServiceProvider::class);
        $app->register(\Illuminate\Database\DatabaseServiceProvider::class);
        $app->register(\Illuminate\Translation\TranslationServiceProvider::class);
        $app->register(\Illuminate\Validation\ValidationServiceProvider::class);
        $app->register(\Illuminate\Routing\RoutingServiceProvider::class);

        // Register authentication and authorization service providers
        $app->register(\Illuminate\Auth\AuthServiceProvider::class);
        $app->register(\Illuminate\Auth\Passwords\PasswordResetServiceProvider::class);
        $app->register(\Illuminate\Hashing\HashServiceProvider::class);

        // Register database and migration service providers
        $app->register(\Illuminate\Database\MigrationServiceProvider::class);
        $app->register(\Illuminate\Foundation\Providers\ConsoleSupportServiceProvider::class);
        $app->register(\Illuminate\Queue\QueueServiceProvider::class);

        // Register logging service provider for debugging
        $app->register(\Illuminate\Log\LogServiceProvider::class);

        // Register Spatie Laravel Settings service provider
        $app->register(\Spatie\LaravelSettings\LaravelSettingsServiceProvider::class);

        // Register application service providers
        $app->register(\App\Providers\RouteServiceProvider::class);

        // Register maintenance mode service provider
        $app->singleton(\Illuminate\Contracts\Foundation\MaintenanceMode::class, \Illuminate\Foundation\FileBasedMaintenanceMode::class);
    })
    ->withMiddleware(function (Middleware $middleware) {
        // Global middleware
        $middleware->use([
            \App\Http\Middleware\TrustProxies::class,
            \Illuminate\Http\Middleware\HandleCors::class,
            \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
            \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
            \App\Http\Middleware\TrimStrings::class,
            \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        ]);

        // Web middleware group
        $middleware->web(append: [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\SetLocale::class,
        ]);

        // API middleware group
        $middleware->api(append: [
            // \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\JsonResponse::class,
        ]);

        // Route middleware aliases
        $middleware->alias([
            'auth' => \App\Http\Middleware\Authenticate::class,
            'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
            'auth.session' => \Illuminate\Session\Middleware\AuthenticateSession::class,
            'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
            'can' => \Illuminate\Auth\Middleware\Authorize::class,
            'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
            'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
            'signed' => \App\Http\Middleware\ValidateSignature::class,
            'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
            'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            'check.account.locked' => \App\Http\Middleware\CheckAccountLocked::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->booted(function () {
        // Configure rate limiting
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    })
    ->create();
