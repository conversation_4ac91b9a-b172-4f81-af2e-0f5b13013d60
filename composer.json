{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2.0", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^12.0", "laravel/nightwatch": "^1.10", "laravel/sanctum": "^4.1", "laravel/socialite": "^5.5", "laravel/tinker": "^2.7", "laravolt/avatar": "^6.0", "spatie/laravel-backup": "^9.0", "spatie/laravel-medialibrary": "^11.0", "spatie/laravel-permission": "^6.0", "spatie/laravel-query-builder": "^6.0", "spatie/laravel-settings": "^3.0", "stripe/stripe-php": "^17.4", "vemcogroup/laravel-translation": "^3.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "fakerphp/faker": "^1.9.1", "laravel-shift/blueprint": "^2.4", "laravel/breeze": "^2.0", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["barryvdh/laravel-debugbar", "protonemedia/laravel-verify-new-email", "spatie/laravel-backup", "spatie/laravel-medialibrary", "spatie/laravel-permission", "spatie/laravel-query-builder", "spatie/laravel-settings", "vemcogroup/laravel-translation", "laravolt/avatar", "spatie/ignition", "spatie/laravel-ignition"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}, "platform": {"php": "8.2.0"}}, "minimum-stability": "dev", "prefer-stable": true}