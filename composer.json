{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1.0", "guzzlehttp/guzzle": "^7.2", "jackiedo/dotenv-editor": "^2.0", "laravel/framework": "^9.42.0", "laravel/sanctum": "^3.0", "laravel/socialite": "^5.5", "laravel/tinker": "^2.7", "laravolt/avatar": "^4.1", "protonemedia/laravel-verify-new-email": "^1.6", "spatie/laravel-backup": "^8.1", "spatie/laravel-medialibrary": "^10.7", "spatie/laravel-permission": "^5.6", "spatie/laravel-query-builder": "^5.1", "spatie/laravel-settings": "^2.6", "vemcogroup/laravel-translation": "^3.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "fakerphp/faker": "^1.9.1", "laravel-shift/blueprint": "^2.4", "laravel/breeze": "^1.14", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}, "platform": {"php": "8.1.0"}}, "minimum-stability": "dev", "prefer-stable": true}