<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('refunds', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('payment_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('user_subscription_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('set null');

            // Stripe refund details
            $table->string('stripe_refund_id')->unique();
            $table->string('stripe_payment_intent_id');
            $table->string('stripe_charge_id')->nullable();

            // Refund amounts (in cents)
            $table->unsignedBigInteger('amount'); // Total refund amount
            $table->unsignedBigInteger('original_amount'); // Original payment amount
            $table->unsignedBigInteger('prorated_amount')->nullable(); // Pro-rated calculation
            $table->string('currency', 3)->default('usd');

            // Refund details
            $table->enum('type', ['full', 'partial', 'prorated'])->default('full');
            $table->enum('reason', [
                'requested_by_customer',
                'duplicate',
                'fraudulent',
                'subscription_cancellation',
                'prorated_downgrade',
                'prorated_cancellation',
                'other'
            ])->default('requested_by_customer');

            $table->enum('status', [
                'pending',
                'succeeded',
                'failed',
                'canceled',
                'requires_action'
            ])->default('pending');

            // Pro-ration calculation details
            $table->json('proration_details')->nullable(); // Store calculation breakdown
            $table->date('service_start_date')->nullable(); // Start of billing period
            $table->date('service_end_date')->nullable(); // End of billing period
            $table->date('cancellation_date')->nullable(); // When service was cancelled
            $table->integer('days_used')->nullable(); // Days of service used
            $table->integer('total_days')->nullable(); // Total days in billing period

            // Administrative details
            $table->text('description')->nullable();
            $table->text('internal_notes')->nullable();
            $table->unsignedBigInteger('processed_by')->nullable(); // Admin who processed
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('stripe_created_at')->nullable();

            // Audit trail
            $table->json('metadata')->nullable(); // Additional data
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'status']);
            $table->index(['stripe_refund_id']);
            $table->index(['type', 'status']);
            $table->index(['created_at']);

            // Foreign key for admin who processed
            $table->foreign('processed_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('refunds');
    }
};
