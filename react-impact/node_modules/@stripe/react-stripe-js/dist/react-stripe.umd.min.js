!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactStripe={},e.React)}(this,(function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){c(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},c=Object.keys(e);for(r=0;r<c.length;r++)n=c[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(e);for(r=0;r<c.length;r++)n=c[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null==n)return;var r,o,c=[],u=!0,i=!1;try{for(n=n.call(e);!(u=(r=n.next()).done)&&(c.push(r.value),!t||c.length!==t);u=!0);}catch(e){i=!0,o=e}finally{try{u||null==n.return||n.return()}finally{if(i)throw o}}return c}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function a(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var l,p,f,d,h={exports:{}};h.exports=function(){if(d)return f;d=1;var e=p?l:(p=1,l="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");function t(){}function n(){}return n.resetWarningCache=t,f=function(){function r(t,n,r,o,c,u){if(u!==e){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function o(){return r}r.isRequired=r;var c={array:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:o,element:r,elementType:r,instanceOf:o,node:r,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:n,resetWarningCache:t};return c.PropTypes=c,c}}()();var m=a(h.exports),y=function(e,n,r){var o=!!r,c=t.useRef(r);t.useEffect((function(){c.current=r}),[r]),t.useEffect((function(){if(!o||!e)return function(){};var t=function(){c.current&&c.current.apply(c,arguments)};return e.on(n,t),function(){e.off(n,t)}}),[o,n,e,c])},g=function(e){var n=t.useRef(e);return t.useEffect((function(){n.current=e}),[e]),n.current},v=function(e){return null!==e&&"object"===o(e)},C="[object Object]",E=function e(t,n){if(!v(t)||!v(n))return t===n;var r=Array.isArray(t);if(r!==Array.isArray(n))return!1;var o=Object.prototype.toString.call(t)===C;if(o!==(Object.prototype.toString.call(n)===C))return!1;if(!o&&!r)return t===n;var c=Object.keys(t),u=Object.keys(n);if(c.length!==u.length)return!1;for(var i={},s=0;s<c.length;s+=1)i[c[s]]=!0;for(var a=0;a<u.length;a+=1)i[u[a]]=!0;var l=Object.keys(i);if(l.length!==c.length)return!1;var p=t,f=n;return l.every((function(t){return e(p[t],f[t])}))},b=function(e,t,n){return v(e)?Object.keys(e).reduce((function(o,u){var i=!v(t)||!E(e[u],t[u]);return n.includes(u)?(i&&console.warn("Unsupported prop change: options.".concat(u," is not a mutable property.")),o):i?r(r({},o||{}),{},c({},u,e[u])):o}),null):null},k="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",S=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:k;if(null===e||v(t=e)&&"function"==typeof t.elements&&"function"==typeof t.createToken&&"function"==typeof t.createPaymentMethod&&"function"==typeof t.confirmCardPayment)return e;throw new Error(n)},w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:k;if(function(e){return v(e)&&"function"==typeof e.then}(e))return{tag:"async",stripePromise:Promise.resolve(e).then((function(e){return S(e,t)}))};var n=S(e,t);return null===n?{tag:"empty"}:{tag:"sync",stripe:n}},P=function(e){e&&e._registerWrapper&&e.registerAppInfo&&(e._registerWrapper({name:"react-stripe-js",version:"3.7.0"}),e.registerAppInfo({name:"react-stripe-js",version:"3.7.0",url:"https://stripe.com/docs/stripe-js/react"}))},O=t.createContext(null);O.displayName="ElementsContext";var j=function(e,t){if(!e)throw new Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},x=function(e){var n=e.stripe,r=e.options,o=e.children,c=t.useMemo((function(){return w(n)}),[n]),u=i(t.useState((function(){return{stripe:"sync"===c.tag?c.stripe:null,elements:"sync"===c.tag?c.stripe.elements(r):null}})),2),s=u[0],a=u[1];t.useEffect((function(){var e=!0,t=function(e){a((function(t){return t.stripe?t:{stripe:e,elements:e.elements(r)}}))};return"async"!==c.tag||s.stripe?"sync"!==c.tag||s.stripe||t(c.stripe):c.stripePromise.then((function(n){n&&e&&t(n)})),function(){e=!1}}),[c,s,r]);var l=g(n);t.useEffect((function(){null!==l&&l!==n&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")}),[l,n]);var p=g(r);return t.useEffect((function(){if(s.elements){var e=b(r,p,["clientSecret","fonts"]);e&&s.elements.update(e)}}),[r,p,s.elements]),t.useEffect((function(){P(s.stripe)}),[s.stripe]),t.createElement(O.Provider,{value:s},o)};x.propTypes={stripe:m.any,options:m.object};var A=function(e){var n=t.useContext(O);return j(n,e)},R=function(e){return(0,e.children)(A("mounts <ElementsConsumer>"))};R.propTypes={children:m.func.isRequired};var I=["on","session"],N=t.createContext(null);N.displayName="CheckoutSdkContext";var T=function(e,t){if(!e)throw new Error("Could not find CheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CheckoutProvider> provider."));return e},Y=t.createContext(null);Y.displayName="CheckoutContext";var B=function(e){var n=e.stripe,r=e.options,o=e.children,c=t.useMemo((function(){return w(n,"Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")}),[n]),s=i(t.useState(null),2),a=s[0],l=s[1],p=i(t.useState((function(){return{stripe:"sync"===c.tag?c.stripe:null,checkoutSdk:null}})),2),f=p[0],d=p[1],h=function(e,t){d((function(n){return n.stripe&&n.checkoutSdk?n:{stripe:e,checkoutSdk:t}}))},m=t.useRef(!1);t.useEffect((function(){var e=!0;return"async"!==c.tag||f.stripe?"sync"===c.tag&&c.stripe&&!m.current&&(m.current=!0,c.stripe.initCheckout(r).then((function(e){e&&(h(c.stripe,e),e.on("change",l))}))):c.stripePromise.then((function(t){t&&e&&!m.current&&(m.current=!0,t.initCheckout(r).then((function(e){e&&(h(t,e),e.on("change",l))})))})),function(){e=!1}}),[c,f,r,l]);var y=g(n);t.useEffect((function(){null!==y&&y!==n&&console.warn("Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.")}),[y,n]);var v=g(r),C=g(f.checkoutSdk);t.useEffect((function(){var e,t;if(f.checkoutSdk){var n=null==v||null===(e=v.elementsOptions)||void 0===e?void 0:e.appearance,o=null==r||null===(t=r.elementsOptions)||void 0===t?void 0:t.appearance,c=!E(o,n),u=!C&&f.checkoutSdk;o&&(c||u)&&f.checkoutSdk.changeAppearance(o)}}),[r,v,f.checkoutSdk,C]),t.useEffect((function(){P(f.stripe)}),[f.stripe]);var b=t.useMemo((function(){return function(e,t){if(!e)return null;e.on,e.session;var n=u(e,I);return t?Object.assign(t,n):Object.assign(e.session(),n)}(f.checkoutSdk,a)}),[f.checkoutSdk,a]);return f.checkoutSdk?t.createElement(N.Provider,{value:f},t.createElement(Y.Provider,{value:b},o)):null};B.propTypes={stripe:m.any,options:m.shape({fetchClientSecret:m.func.isRequired,elementsOptions:m.object}).isRequired};var U=function(e){var n=t.useContext(N),r=t.useContext(O);if(n&&r)throw new Error("You cannot wrap the part of your app that ".concat(e," in both <CheckoutProvider> and <Elements> providers."));return n?T(n,e):j(r,e)},M=["mode"],_=function(e,n){var r,o="".concat((r=e).charAt(0).toUpperCase()+r.slice(1),"Element"),c=n?function(e){U("mounts <".concat(o,">"));var n=e.id,r=e.className;return t.createElement("div",{id:n,className:r})}:function(n){var r,c=n.id,s=n.className,a=n.options,l=void 0===a?{}:a,p=n.onBlur,f=n.onFocus,d=n.onReady,h=n.onChange,m=n.onEscape,v=n.onClick,C=n.onLoadError,E=n.onLoaderStart,k=n.onNetworksChange,S=n.onConfirm,w=n.onCancel,P=n.onShippingAddressChange,O=n.onShippingRateChange,j=U("mounts <".concat(o,">")),x="elements"in j?j.elements:null,A="checkoutSdk"in j?j.checkoutSdk:null,R=i(t.useState(null),2),I=R[0],N=R[1],T=t.useRef(null),Y=t.useRef(null);y(I,"blur",p),y(I,"focus",f),y(I,"escape",m),y(I,"click",v),y(I,"loaderror",C),y(I,"loaderstart",E),y(I,"networkschange",k),y(I,"confirm",S),y(I,"cancel",w),y(I,"shippingaddresschange",P),y(I,"shippingratechange",O),y(I,"change",h),d&&(r="expressCheckout"===e?d:function(){d(I)}),y(I,"ready",r),t.useLayoutEffect((function(){if(null===T.current&&null!==Y.current&&(x||A)){var t=null;if(A)switch(e){case"payment":t=A.createPaymentElement(l);break;case"address":if(!("mode"in l))throw new Error("You must supply options.mode. mode must be 'billing' or 'shipping'.");var n=l.mode,r=u(l,M);if("shipping"===n)t=A.createShippingAddressElement(r);else{if("billing"!==n)throw new Error("Invalid options.mode. mode must be 'billing' or 'shipping'.");t=A.createBillingAddressElement(r)}break;case"expressCheckout":t=A.createExpressCheckoutElement(l);break;case"currencySelector":t=A.createCurrencySelectorElement();break;default:throw new Error("Invalid Element type ".concat(o,". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />."))}else x&&(t=x.create(e,l));T.current=t,N(t),t&&t.mount(Y.current)}}),[x,A,l]);var B=g(l);return t.useEffect((function(){if(T.current){var e=b(l,B,["paymentRequest"]);e&&"update"in T.current&&T.current.update(e)}}),[l,B]),t.useLayoutEffect((function(){return function(){if(T.current&&"function"==typeof T.current.destroy)try{T.current.destroy(),T.current=null}catch(e){}}}),[]),t.createElement("div",{id:c,className:s,ref:Y})};return c.propTypes={id:m.string,className:m.string,onChange:m.func,onBlur:m.func,onFocus:m.func,onReady:m.func,onEscape:m.func,onClick:m.func,onLoadError:m.func,onLoaderStart:m.func,onNetworksChange:m.func,onConfirm:m.func,onCancel:m.func,onShippingAddressChange:m.func,onShippingRateChange:m.func,options:m.object},c.displayName=o,c.__elementType=e,c},L="undefined"==typeof window,D=t.createContext(null);D.displayName="EmbeddedCheckoutProviderContext";var q=function(){var e=t.useContext(D);if(!e)throw new Error("<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>");return e},W=L?function(e){var n=e.id,r=e.className;return q(),t.createElement("div",{id:n,className:r})}:function(e){var n=e.id,r=e.className,o=q().embeddedCheckout,c=t.useRef(!1),u=t.useRef(null);return t.useLayoutEffect((function(){return!c.current&&o&&null!==u.current&&(o.mount(u.current),c.current=!0),function(){if(c.current&&o)try{o.unmount(),c.current=!1}catch(e){}}}),[o]),t.createElement("div",{ref:u,id:n,className:r})},F=_("auBankAccount",L),H=_("card",L),V=_("cardNumber",L),$=_("cardExpiry",L),z=_("cardCvc",L),G=_("fpxBank",L),J=_("iban",L),K=_("idealBank",L),Q=_("p24Bank",L),X=_("epsBank",L),Z=_("payment",L),ee=_("expressCheckout",L),te=_("currencySelector",L),ne=_("paymentRequestButton",L),re=_("linkAuthentication",L),oe=_("address",L),ce=_("shippingAddress",L),ue=_("paymentMethodMessaging",L),ie=_("affirmMessage",L),se=_("afterpayClearpayMessage",L);e.AddressElement=oe,e.AffirmMessageElement=ie,e.AfterpayClearpayMessageElement=se,e.AuBankAccountElement=F,e.CardCvcElement=z,e.CardElement=H,e.CardExpiryElement=$,e.CardNumberElement=V,e.CheckoutProvider=B,e.CurrencySelectorElement=te,e.Elements=x,e.ElementsConsumer=R,e.EmbeddedCheckout=W,e.EmbeddedCheckoutProvider=function(e){var n=e.stripe,r=e.options,o=e.children,c=t.useMemo((function(){return w(n,"Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")}),[n]),u=t.useRef(null),s=t.useRef(null),a=i(t.useState({embeddedCheckout:null}),2),l=a[0],p=a[1];t.useEffect((function(){if(!s.current&&!u.current){var e=function(e){s.current||u.current||(s.current=e,u.current=s.current.initEmbeddedCheckout(r).then((function(e){p({embeddedCheckout:e})})))};"async"!==c.tag||s.current||!r.clientSecret&&!r.fetchClientSecret?"sync"!==c.tag||s.current||!r.clientSecret&&!r.fetchClientSecret||e(c.stripe):c.stripePromise.then((function(t){t&&e(t)}))}}),[c,r,l,s]),t.useEffect((function(){return function(){l.embeddedCheckout?(u.current=null,l.embeddedCheckout.destroy()):u.current&&u.current.then((function(){u.current=null,l.embeddedCheckout&&l.embeddedCheckout.destroy()}))}}),[l.embeddedCheckout]),t.useEffect((function(){P(s)}),[s]);var f=g(n);t.useEffect((function(){null!==f&&f!==n&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.")}),[f,n]);var d=g(r);return t.useEffect((function(){null!=d&&(null!=r?(void 0===r.clientSecret&&void 0===r.fetchClientSecret&&console.warn("Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`."),null!=d.clientSecret&&r.clientSecret!==d.clientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=d.fetchClientSecret&&r.fetchClientSecret!==d.fetchClientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=d.onComplete&&r.onComplete!==d.onComplete&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it."),null!=d.onShippingDetailsChange&&r.onShippingDetailsChange!==d.onShippingDetailsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it."),null!=d.onLineItemsChange&&r.onLineItemsChange!==d.onLineItemsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.")):console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them."))}),[d,r]),t.createElement(D.Provider,{value:l},o)},e.EpsBankElement=X,e.ExpressCheckoutElement=ee,e.FpxBankElement=G,e.IbanElement=J,e.IdealBankElement=K,e.LinkAuthenticationElement=re,e.P24BankElement=Q,e.PaymentElement=Z,e.PaymentMethodMessagingElement=ue,e.PaymentRequestButtonElement=ne,e.ShippingAddressElement=ce,e.useCheckout=function(){!function(e){var n=t.useContext(N);T(n,e)}("calls useCheckout()");var e=t.useContext(Y);if(!e)throw new Error("Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.");return e},e.useElements=function(){return A("calls useElements()").elements},e.useStripe=function(){return U("calls useStripe()").stripe}}));
