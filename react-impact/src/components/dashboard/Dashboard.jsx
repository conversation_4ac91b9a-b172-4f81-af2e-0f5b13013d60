import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { subscriptionsAPI, paymentHistoryAPI, accountStatusAPI } from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';
import { 
  CreditCardIcon, 
  DocumentTextIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

const Dashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState({
    subscription: null,
    recentPayments: [],
    accountStatus: null
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const [subscriptionsRes, paymentsRes, statusRes] = await Promise.all([
          subscriptionsAPI.getAll(),
          paymentHistoryAPI.getRecent(),
          accountStatusAPI.getStatus()
        ]);

        setData({
          subscription: subscriptionsRes.data.data?.[0] || null,
          recentPayments: paymentsRes.data.data || [],
          accountStatus: statusRes.data.data || null
        });
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'past_due':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'canceled':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'past_due':
        return 'Past Due';
      case 'canceled':
        return 'Canceled';
      case 'trialing':
        return 'Trial';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.name}!
        </h1>
        <p className="text-gray-600">
          Here's an overview of your account and subscription status.
        </p>
      </div>

      {/* Account Status Alert */}
      {data.accountStatus?.status !== 'active' && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Account Status: {data.accountStatus?.status}
              </h3>
              <p className="mt-1 text-sm text-yellow-700">
                {data.accountStatus?.message || 'Please check your account status.'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Current Subscription */}
        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CreditCardIcon className="h-8 w-8 text-primary-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">Current Plan</h3>
              {data.subscription ? (
                <div className="mt-1">
                  <p className="text-sm text-gray-600">
                    {data.subscription.subscription_product?.name || 'Unknown Plan'}
                  </p>
                  <div className="flex items-center mt-1">
                    {getStatusIcon(data.subscription.status)}
                    <span className="ml-1 text-sm text-gray-600">
                      {getStatusText(data.subscription.status)}
                    </span>
                  </div>
                  {data.subscription.next_billing_date && (
                    <p className="text-xs text-gray-500 mt-1">
                      Next billing: {new Date(data.subscription.next_billing_date).toLocaleDateString()}
                    </p>
                  )}
                </div>
              ) : (
                <p className="text-sm text-gray-500 mt-1">No active subscription</p>
              )}
            </div>
          </div>
          <div className="mt-4">
            <Link
              to="/subscriptions"
              className="text-sm text-primary-600 hover:text-primary-500 font-medium"
            >
              Manage subscription →
            </Link>
          </div>
        </div>

        {/* Recent Payments */}
        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DocumentTextIcon className="h-8 w-8 text-primary-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">Recent Payments</h3>
              <p className="text-sm text-gray-600 mt-1">
                {data.recentPayments.length} recent transactions
              </p>
            </div>
          </div>
          <div className="mt-4">
            <Link
              to="/billing"
              className="text-sm text-primary-600 hover:text-primary-500 font-medium"
            >
              View all payments →
            </Link>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-2">
            <Link
              to="/payment-methods"
              className="block text-sm text-primary-600 hover:text-primary-500"
            >
              • Manage payment methods
            </Link>
            <Link
              to="/subscriptions"
              className="block text-sm text-primary-600 hover:text-primary-500"
            >
              • Upgrade/downgrade plan
            </Link>
            <Link
              to="/billing"
              className="block text-sm text-primary-600 hover:text-primary-500"
            >
              • Download invoices
            </Link>
            <Link
              to="/profile"
              className="block text-sm text-primary-600 hover:text-primary-500"
            >
              • Update profile
            </Link>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      {data.recentPayments.length > 0 && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {data.recentPayments.slice(0, 5).map((payment) => (
              <div key={payment.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Payment {payment.status === 'succeeded' ? 'Successful' : 'Failed'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {new Date(payment.created_at).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    ${payment.amount}
                  </p>
                  <p className={`text-xs ${
                    payment.status === 'succeeded' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {payment.status}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
