import React, { useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon, CreditCardIcon } from '@heroicons/react/24/outline';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import LoadingSpinner from '../common/LoadingSpinner';

const schema = yup.object({
  card_number: yup.string()
    .required('Card number is required')
    .matches(/^\d{13,19}$/, 'Invalid card number'),
  exp_month: yup.string()
    .required('Expiry month is required')
    .matches(/^(0[1-9]|1[0-2])$/, 'Invalid month'),
  exp_year: yup.string()
    .required('Expiry year is required')
    .matches(/^\d{4}$/, 'Invalid year')
    .test('future-year', 'Card has expired', function(value) {
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1;
      const expYear = parseInt(value);
      const expMonth = parseInt(this.parent.exp_month);
      
      if (expYear > currentYear) return true;
      if (expYear === currentYear && expMonth >= currentMonth) return true;
      return false;
    }),
  cvc: yup.string()
    .required('CVC is required')
    .matches(/^\d{3,4}$/, 'Invalid CVC'),
  cardholder_name: yup.string()
    .required('Cardholder name is required')
    .min(2, 'Name must be at least 2 characters'),
});

const AddPaymentMethodModal = ({ isOpen, onClose, onAdd, isProcessing }) => {
  const [error, setError] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm({
    resolver: yupResolver(schema)
  });

  const cardNumber = watch('card_number', '');

  const formatCardNumber = (value) => {
    // Remove all non-digits
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    
    // Add spaces every 4 digits
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const detectCardBrand = (number) => {
    const cleanNumber = number.replace(/\s/g, '');
    
    if (/^4/.test(cleanNumber)) return 'visa';
    if (/^5[1-5]/.test(cleanNumber)) return 'mastercard';
    if (/^3[47]/.test(cleanNumber)) return 'amex';
    if (/^6/.test(cleanNumber)) return 'discover';
    
    return 'unknown';
  };

  const onSubmit = async (data) => {
    setError('');
    
    // Clean card number
    const cleanCardNumber = data.card_number.replace(/\s/g, '');
    
    const paymentMethodData = {
      card_number: cleanCardNumber,
      exp_month: parseInt(data.exp_month),
      exp_year: parseInt(data.exp_year),
      cvc: data.cvc,
      cardholder_name: data.cardholder_name,
    };

    try {
      await onAdd(paymentMethodData);
      reset();
    } catch (error) {
      setError(error.message || 'Failed to add payment method');
    }
  };

  const handleClose = () => {
    reset();
    setError('');
    onClose();
  };

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 20 }, (_, i) => currentYear + i);
  const months = [
    { value: '01', label: '01 - January' },
    { value: '02', label: '02 - February' },
    { value: '03', label: '03 - March' },
    { value: '04', label: '04 - April' },
    { value: '05', label: '05 - May' },
    { value: '06', label: '06 - June' },
    { value: '07', label: '07 - July' },
    { value: '08', label: '08 - August' },
    { value: '09', label: '09 - September' },
    { value: '10', label: '10 - October' },
    { value: '11', label: '11 - November' },
    { value: '12', label: '12 - December' },
  ];

  return (
    <Transition show={isOpen} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500"
                    onClick={handleClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-primary-100 sm:mx-0 sm:h-10 sm:w-10">
                    <CreditCardIcon className="h-6 w-6 text-primary-600" />
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-lg font-semibold leading-6 text-gray-900">
                      Add Payment Method
                    </Dialog.Title>

                    <form onSubmit={handleSubmit(onSubmit)} className="mt-6">
                      {error && (
                        <div className="mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                          {error}
                        </div>
                      )}

                      <div className="space-y-4">
                        <div>
                          <label htmlFor="cardholder_name" className="label">
                            Cardholder Name
                          </label>
                          <input
                            {...register('cardholder_name')}
                            type="text"
                            className={`input-field ${errors.cardholder_name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            placeholder="John Doe"
                          />
                          {errors.cardholder_name && (
                            <p className="mt-1 text-sm text-red-600">{errors.cardholder_name.message}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="card_number" className="label">
                            Card Number
                          </label>
                          <input
                            {...register('card_number', {
                              onChange: (e) => {
                                e.target.value = formatCardNumber(e.target.value);
                              }
                            })}
                            type="text"
                            maxLength="19"
                            className={`input-field ${errors.card_number ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            placeholder="1234 5678 9012 3456"
                          />
                          {errors.card_number && (
                            <p className="mt-1 text-sm text-red-600">{errors.card_number.message}</p>
                          )}
                          {cardNumber && (
                            <p className="mt-1 text-xs text-gray-500">
                              Detected: {detectCardBrand(cardNumber).toUpperCase()}
                            </p>
                          )}
                        </div>

                        <div className="grid grid-cols-3 gap-4">
                          <div>
                            <label htmlFor="exp_month" className="label">
                              Month
                            </label>
                            <select
                              {...register('exp_month')}
                              className={`input-field ${errors.exp_month ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            >
                              <option value="">Month</option>
                              {months.map((month) => (
                                <option key={month.value} value={month.value}>
                                  {month.label}
                                </option>
                              ))}
                            </select>
                            {errors.exp_month && (
                              <p className="mt-1 text-sm text-red-600">{errors.exp_month.message}</p>
                            )}
                          </div>

                          <div>
                            <label htmlFor="exp_year" className="label">
                              Year
                            </label>
                            <select
                              {...register('exp_year')}
                              className={`input-field ${errors.exp_year ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            >
                              <option value="">Year</option>
                              {years.map((year) => (
                                <option key={year} value={year}>
                                  {year}
                                </option>
                              ))}
                            </select>
                            {errors.exp_year && (
                              <p className="mt-1 text-sm text-red-600">{errors.exp_year.message}</p>
                            )}
                          </div>

                          <div>
                            <label htmlFor="cvc" className="label">
                              CVC
                            </label>
                            <input
                              {...register('cvc')}
                              type="text"
                              maxLength="4"
                              className={`input-field ${errors.cvc ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                              placeholder="123"
                            />
                            {errors.cvc && (
                              <p className="mt-1 text-sm text-red-600">{errors.cvc.message}</p>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="mt-6 flex space-x-3">
                        <button
                          type="button"
                          onClick={handleClose}
                          className="btn-secondary flex-1"
                          disabled={isProcessing}
                        >
                          Cancel
                        </button>
                        <button
                          type="submit"
                          disabled={isProcessing}
                          className="btn-primary flex-1"
                        >
                          {isProcessing ? (
                            <LoadingSpinner size="sm" />
                          ) : (
                            'Add Payment Method'
                          )}
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default AddPaymentMethodModal;
