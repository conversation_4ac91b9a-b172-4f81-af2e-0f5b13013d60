import React from 'react';
import { 
  CreditCardIcon, 
  StarIcon,
  TrashIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

const PaymentMethodCard = ({ paymentMethod, onSetDefault, onDelete, isProcessing }) => {
  const getBrandIcon = (brand) => {
    // You could add specific brand icons here
    return <CreditCardIcon className="h-8 w-8 text-gray-400" />;
  };

  const getBrandColor = (brand) => {
    switch (brand?.toLowerCase()) {
      case 'visa':
        return 'text-blue-600';
      case 'mastercard':
        return 'text-red-600';
      case 'amex':
        return 'text-green-600';
      case 'discover':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatExpiryDate = (month, year) => {
    return `${String(month).padStart(2, '0')}/${String(year).slice(-2)}`;
  };

  const isExpired = () => {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    return paymentMethod.exp_year < currentYear || 
           (paymentMethod.exp_year === currentYear && paymentMethod.exp_month < currentMonth);
  };

  const isExpiringSoon = () => {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    // Check if expiring within 2 months
    const expiryDate = new Date(paymentMethod.exp_year, paymentMethod.exp_month - 1);
    const twoMonthsFromNow = new Date(currentYear, currentMonth + 1);
    
    return expiryDate <= twoMonthsFromNow && !isExpired();
  };

  return (
    <div className={`border rounded-lg p-4 ${
      paymentMethod.is_default ? 'border-primary-200 bg-primary-50' : 'border-gray-200'
    }`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            {getBrandIcon(paymentMethod.brand)}
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h3 className={`text-lg font-medium ${getBrandColor(paymentMethod.brand)}`}>
                {paymentMethod.brand?.toUpperCase() || 'CARD'}
              </h3>
              {paymentMethod.is_default && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-100 text-primary-800">
                  <StarIconSolid className="h-3 w-3 mr-1" />
                  Default
                </span>
              )}
              {isExpired() && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                  Expired
                </span>
              )}
              {isExpiringSoon() && !isExpired() && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                  Expiring Soon
                </span>
              )}
            </div>
            
            <div className="mt-1">
              <p className="text-sm text-gray-900">
                •••• •••• •••• {paymentMethod.last_four}
              </p>
              <p className="text-sm text-gray-500">
                Expires {formatExpiryDate(paymentMethod.exp_month, paymentMethod.exp_year)}
              </p>
            </div>

            {paymentMethod.billing_details?.name && (
              <p className="text-sm text-gray-600 mt-1">
                {paymentMethod.billing_details.name}
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {!paymentMethod.is_default && (
            <button
              onClick={() => onSetDefault(paymentMethod.id)}
              disabled={isProcessing}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Set as default"
            >
              <StarIcon className="h-4 w-4 mr-1" />
              Set Default
            </button>
          )}
          
          <button
            onClick={() => onDelete(paymentMethod.id)}
            disabled={isProcessing || paymentMethod.is_default}
            className="inline-flex items-center px-3 py-1.5 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
            title={paymentMethod.is_default ? "Cannot delete default payment method" : "Delete payment method"}
          >
            <TrashIcon className="h-4 w-4 mr-1" />
            Delete
          </button>
        </div>
      </div>

      {/* Additional Information */}
      {(isExpired() || isExpiringSoon()) && (
        <div className={`mt-3 p-3 rounded-md ${
          isExpired() ? 'bg-red-50 border border-red-200' : 'bg-yellow-50 border border-yellow-200'
        }`}>
          <div className="flex">
            <div className={`flex-shrink-0 ${isExpired() ? 'text-red-400' : 'text-yellow-400'}`}>
              <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className={`text-sm font-medium ${
                isExpired() ? 'text-red-800' : 'text-yellow-800'
              }`}>
                {isExpired() ? 'Card Expired' : 'Card Expiring Soon'}
              </h3>
              <p className={`mt-1 text-sm ${
                isExpired() ? 'text-red-700' : 'text-yellow-700'
              }`}>
                {isExpired() 
                  ? 'This payment method has expired and cannot be used for payments.'
                  : 'This payment method will expire soon. Please update your card information.'
                }
              </p>
            </div>
          </div>
        </div>
      )}

      {paymentMethod.is_default && (
        <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-blue-400" />
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                This is your default payment method and will be used for all future charges.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentMethodCard;
