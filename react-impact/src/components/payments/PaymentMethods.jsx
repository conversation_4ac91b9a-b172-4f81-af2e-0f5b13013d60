import React, { useState, useEffect } from 'react';
import { paymentMethodsAPI } from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';
import PaymentMethodCard from './PaymentMethodCard';
import AddPaymentMethodModal from './AddPaymentMethodModal';
import { 
  CreditCardIcon, 
  PlusIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

const PaymentMethods = () => {
  const [loading, setLoading] = useState(true);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [alerts, setAlerts] = useState([]);

  useEffect(() => {
    fetchPaymentMethods();
    handleUrlParameters();
  }, []);

  const handleUrlParameters = () => {
    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.get('success') === 'payment_method_added') {
      addAlert('Payment method added successfully!', 'success');
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (urlParams.get('error')) {
      const errorType = urlParams.get('error');
      let errorMessage = 'Failed to add payment method';

      if (errorType === 'checkout_failed') {
        errorMessage = 'Failed to create checkout session';
      } else if (errorType === 'processing_failed') {
        errorMessage = 'Failed to process payment method';
      }

      addAlert(errorMessage, 'error');
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (urlParams.get('cancelled') === 'true') {
      addAlert('Payment method setup was cancelled', 'warning');
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  };

  const fetchPaymentMethods = async () => {
    try {
      const response = await paymentMethodsAPI.getAll();
      setPaymentMethods(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch payment methods:', error);
      addAlert('Failed to load payment methods', 'error');
    } finally {
      setLoading(false);
    }
  };

  const addAlert = (message, type = 'info') => {
    const alert = { id: Date.now(), message, type };
    setAlerts(prev => [...prev, alert]);
    setTimeout(() => {
      setAlerts(prev => prev.filter(a => a.id !== alert.id));
    }, 5000);
  };

  const handleAddPaymentMethod = async (paymentMethodData) => {
    setIsProcessing(true);
    try {
      const response = await paymentMethodsAPI.create(paymentMethodData);
      if (response.data.success) {
        addAlert('Payment method added successfully!', 'success');
        await fetchPaymentMethods();
        setIsAddModalOpen(false);
      }
    } catch (error) {
      console.error('Failed to add payment method:', error);
      addAlert(error.response?.data?.message || 'Failed to add payment method', 'error');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSetDefault = async (paymentMethodId) => {
    setIsProcessing(true);
    try {
      const response = await paymentMethodsAPI.setDefault(paymentMethodId);
      if (response.data.success) {
        addAlert('Default payment method updated!', 'success');
        await fetchPaymentMethods();
      }
    } catch (error) {
      console.error('Failed to set default payment method:', error);
      addAlert(error.response?.data?.message || 'Failed to update default payment method', 'error');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDelete = async (paymentMethodId) => {
    if (!confirm('Are you sure you want to delete this payment method?')) {
      return;
    }

    setIsProcessing(true);
    try {
      const response = await paymentMethodsAPI.delete(paymentMethodId);
      if (response.data.success) {
        addAlert('Payment method deleted successfully!', 'success');
        await fetchPaymentMethods();
      }
    } catch (error) {
      console.error('Failed to delete payment method:', error);
      addAlert(error.response?.data?.message || 'Failed to delete payment method', 'error');
    } finally {
      setIsProcessing(false);
    }
  };

  const getAlertIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-400" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />;
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-blue-400" />;
    }
  };

  const getAlertColors = (type) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Methods</h1>
          <p className="text-gray-600">
            Manage your payment methods and billing preferences.
          </p>
        </div>
        <button
          onClick={() => setIsAddModalOpen(true)}
          disabled={isProcessing}
          className="btn-primary flex items-center"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Payment Method
        </button>
      </div>

      {/* Alerts */}
      {alerts.map((alert) => (
        <div key={alert.id} className={`border rounded-md p-4 ${getAlertColors(alert.type)}`}>
          <div className="flex">
            {getAlertIcon(alert.type)}
            <div className="ml-3">
              <p className="text-sm font-medium">{alert.message}</p>
            </div>
          </div>
        </div>
      ))}

      {/* Payment Methods List */}
      <div className="card">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Your Payment Methods</h2>
        
        {paymentMethods.length === 0 ? (
          <div className="text-center py-12">
            <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No payment methods</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by adding your first payment method.
            </p>
            <div className="mt-6">
              <button
                onClick={() => setIsAddModalOpen(true)}
                className="btn-primary"
              >
                <PlusIcon className="h-5 w-5 mr-2" />
                Add Payment Method
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {paymentMethods.map((method) => (
              <PaymentMethodCard
                key={method.id}
                paymentMethod={method}
                onSetDefault={handleSetDefault}
                onDelete={handleDelete}
                isProcessing={isProcessing}
              />
            ))}
          </div>
        )}
      </div>

      {/* Security Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Security & Privacy
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                Your payment information is securely stored and encrypted. We use Stripe to process payments and never store your full credit card details on our servers.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Add Payment Method Modal */}
      <AddPaymentMethodModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddPaymentMethod}
        isProcessing={isProcessing}
      />
    </div>
  );
};

export default PaymentMethods;
