import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAuth } from '../../contexts/AuthContext';
import { userAPI } from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';
import { 
  UserIcon, 
  KeyIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';

const profileSchema = yup.object({
  name: yup.string().required('Name is required').min(2, 'Name must be at least 2 characters'),
  email: yup.string().email('Invalid email').required('Email is required'),
});

const passwordSchema = yup.object({
  current_password: yup.string().required('Current password is required'),
  password: yup.string()
    .required('New password is required')
    .min(8, 'Password must be at least 8 characters'),
  password_confirmation: yup.string()
    .required('Password confirmation is required')
    .oneOf([yup.ref('password')], 'Passwords must match'),
});

const Profile = () => {
  const { user, updateUser } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [isUpdating, setIsUpdating] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  const [alerts, setAlerts] = useState([]);

  const profileForm = useForm({
    resolver: yupResolver(profileSchema),
    defaultValues: {
      name: user?.name || '',
      email: user?.email || ''
    }
  });

  const passwordForm = useForm({
    resolver: yupResolver(passwordSchema)
  });

  const addAlert = (message, type = 'info') => {
    const alert = { id: Date.now(), message, type };
    setAlerts(prev => [...prev, alert]);
    setTimeout(() => {
      setAlerts(prev => prev.filter(a => a.id !== alert.id));
    }, 5000);
  };

  const onProfileSubmit = async (data) => {
    setIsUpdating(true);
    try {
      const response = await userAPI.updateProfile(data);
      if (response.data.success) {
        updateUser(response.data.data);
        addAlert('Profile updated successfully!', 'success');
      }
    } catch (error) {
      console.error('Failed to update profile:', error);
      addAlert(error.response?.data?.message || 'Failed to update profile', 'error');
    } finally {
      setIsUpdating(false);
    }
  };

  const onPasswordSubmit = async (data) => {
    setIsUpdating(true);
    try {
      const response = await userAPI.updatePassword(data);
      if (response.data.success) {
        passwordForm.reset();
        addAlert('Password updated successfully!', 'success');
      }
    } catch (error) {
      console.error('Failed to update password:', error);
      addAlert(error.response?.data?.message || 'Failed to update password', 'error');
    } finally {
      setIsUpdating(false);
    }
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const getAlertIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-400" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />;
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-blue-400" />;
    }
  };

  const getAlertColors = (type) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Profile Settings</h1>
        <p className="text-gray-600">
          Manage your account information and security settings.
        </p>
      </div>

      {/* Alerts */}
      {alerts.map((alert) => (
        <div key={alert.id} className={`border rounded-md p-4 ${getAlertColors(alert.type)}`}>
          <div className="flex">
            {getAlertIcon(alert.type)}
            <div className="ml-3">
              <p className="text-sm font-medium">{alert.message}</p>
            </div>
          </div>
        </div>
      ))}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('profile')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'profile'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <UserIcon className="h-5 w-5 inline mr-2" />
            Profile Information
          </button>
          <button
            onClick={() => setActiveTab('password')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'password'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <KeyIcon className="h-5 w-5 inline mr-2" />
            Change Password
          </button>
        </nav>
      </div>

      {/* Content */}
      <div className="card">
        {activeTab === 'profile' ? (
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Profile Information</h2>
            
            <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-6">
              <div>
                <label htmlFor="name" className="label">
                  Full Name
                </label>
                <input
                  {...profileForm.register('name')}
                  type="text"
                  className={`input-field ${profileForm.formState.errors.name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                  placeholder="Enter your full name"
                />
                {profileForm.formState.errors.name && (
                  <p className="mt-1 text-sm text-red-600">{profileForm.formState.errors.name.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="email" className="label">
                  Email Address
                </label>
                <input
                  {...profileForm.register('email')}
                  type="email"
                  className={`input-field ${profileForm.formState.errors.email ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                  placeholder="Enter your email address"
                />
                {profileForm.formState.errors.email && (
                  <p className="mt-1 text-sm text-red-600">{profileForm.formState.errors.email.message}</p>
                )}
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isUpdating}
                  className="btn-primary"
                >
                  {isUpdating ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    'Update Profile'
                  )}
                </button>
              </div>
            </form>
          </div>
        ) : (
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Change Password</h2>
            
            <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-6">
              <div>
                <label htmlFor="current_password" className="label">
                  Current Password
                </label>
                <div className="relative">
                  <input
                    {...passwordForm.register('current_password')}
                    type={showPasswords.current ? 'text' : 'password'}
                    className={`input-field pr-10 ${passwordForm.formState.errors.current_password ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="Enter your current password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => togglePasswordVisibility('current')}
                  >
                    {showPasswords.current ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {passwordForm.formState.errors.current_password && (
                  <p className="mt-1 text-sm text-red-600">{passwordForm.formState.errors.current_password.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="password" className="label">
                  New Password
                </label>
                <div className="relative">
                  <input
                    {...passwordForm.register('password')}
                    type={showPasswords.new ? 'text' : 'password'}
                    className={`input-field pr-10 ${passwordForm.formState.errors.password ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="Enter your new password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => togglePasswordVisibility('new')}
                  >
                    {showPasswords.new ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {passwordForm.formState.errors.password && (
                  <p className="mt-1 text-sm text-red-600">{passwordForm.formState.errors.password.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="password_confirmation" className="label">
                  Confirm New Password
                </label>
                <div className="relative">
                  <input
                    {...passwordForm.register('password_confirmation')}
                    type={showPasswords.confirm ? 'text' : 'password'}
                    className={`input-field pr-10 ${passwordForm.formState.errors.password_confirmation ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="Confirm your new password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => togglePasswordVisibility('confirm')}
                  >
                    {showPasswords.confirm ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {passwordForm.formState.errors.password_confirmation && (
                  <p className="mt-1 text-sm text-red-600">{passwordForm.formState.errors.password_confirmation.message}</p>
                )}
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isUpdating}
                  className="btn-primary"
                >
                  {isUpdating ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    'Update Password'
                  )}
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
};

export default Profile;
