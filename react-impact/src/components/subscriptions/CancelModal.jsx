import React, { useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../common/LoadingSpinner';

const CancelModal = ({ isOpen, onClose, subscription, onCancel, isProcessing }) => {
  const [reason, setReason] = useState('');
  const [feedback, setFeedback] = useState('');

  const reasons = [
    'Too expensive',
    'Not using the service enough',
    'Found a better alternative',
    'Technical issues',
    'Poor customer service',
    'No longer need the service',
    'Other'
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    onCancel({ reason, feedback });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Transition show={isOpen} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-lg font-semibold leading-6 text-gray-900">
                      Cancel Subscription
                    </Dialog.Title>

                    {subscription && (
                      <div className="mt-4">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900">
                            {subscription.subscription_product?.name}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">
                            {formatCurrency(subscription.subscription_product?.price)}/{subscription.subscription_product?.billing_interval}
                          </p>
                          {subscription.current_period_end && (
                            <p className="text-sm text-gray-600 mt-1">
                              Access until: {formatDate(subscription.current_period_end)}
                            </p>
                          )}
                        </div>

                        <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-4">
                          <div className="flex">
                            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                            <div className="ml-3">
                              <h3 className="text-sm font-medium text-yellow-800">
                                Important Information
                              </h3>
                              <div className="mt-2 text-sm text-yellow-700">
                                <ul className="list-disc list-inside space-y-1">
                                  <li>Your subscription will be canceled immediately</li>
                                  <li>You'll receive a pro-rated refund for unused time</li>
                                  <li>You'll lose access to premium features</li>
                                  <li>This action cannot be undone</li>
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>

                        <form onSubmit={handleSubmit} className="mt-6">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Reason for canceling (optional)
                            </label>
                            <div className="space-y-2">
                              {reasons.map((reasonOption) => (
                                <label
                                  key={reasonOption}
                                  className="flex items-center"
                                >
                                  <input
                                    type="radio"
                                    name="reason"
                                    value={reasonOption}
                                    checked={reason === reasonOption}
                                    onChange={(e) => setReason(e.target.value)}
                                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                                  />
                                  <span className="ml-2 text-sm text-gray-700">
                                    {reasonOption}
                                  </span>
                                </label>
                              ))}
                            </div>
                          </div>

                          <div className="mt-4">
                            <label htmlFor="feedback" className="block text-sm font-medium text-gray-700 mb-2">
                              Additional feedback (optional)
                            </label>
                            <textarea
                              id="feedback"
                              rows={3}
                              value={feedback}
                              onChange={(e) => setFeedback(e.target.value)}
                              className="input-field"
                              placeholder="Help us improve by sharing your feedback..."
                            />
                          </div>

                          <div className="mt-6 flex space-x-3">
                            <button
                              type="button"
                              onClick={onClose}
                              className="btn-secondary flex-1"
                              disabled={isProcessing}
                            >
                              Keep Subscription
                            </button>
                            <button
                              type="submit"
                              disabled={isProcessing}
                              className="btn-danger flex-1"
                            >
                              {isProcessing ? (
                                <LoadingSpinner size="sm" />
                              ) : (
                                'Cancel Subscription'
                              )}
                            </button>
                          </div>
                        </form>
                      </div>
                    )}
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default CancelModal;
