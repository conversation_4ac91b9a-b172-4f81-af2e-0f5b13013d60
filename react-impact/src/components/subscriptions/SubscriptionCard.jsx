import React from 'react';
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  ClockIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

const SubscriptionCard = ({ subscription, onCancel, isProcessing }) => {
  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'past_due':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'canceled':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'trialing':
        return <ClockIcon className="h-5 w-5 text-blue-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'past_due':
        return 'Past Due';
      case 'canceled':
        return 'Canceled';
      case 'trialing':
        return 'Trial Period';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'past_due':
        return 'text-yellow-600 bg-yellow-100';
      case 'canceled':
        return 'text-red-600 bg-red-100';
      case 'trialing':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="border border-gray-200 rounded-lg p-6">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-medium text-gray-900">
              {subscription.subscription_product?.name || 'Unknown Plan'}
            </h3>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
              <span className="mr-1">{getStatusIcon(subscription.status)}</span>
              {getStatusText(subscription.status)}
            </span>
          </div>
          
          <p className="text-gray-600 mt-1">
            {subscription.subscription_product?.description}
          </p>

          <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <dt className="text-sm font-medium text-gray-500">Price</dt>
              <dd className="text-lg font-semibold text-gray-900">
                {formatCurrency(subscription.subscription_product?.price || 0)}
                <span className="text-sm font-normal text-gray-600">
                  /{subscription.subscription_product?.billing_interval || 'month'}
                </span>
              </dd>
            </div>

            <div>
              <dt className="text-sm font-medium text-gray-500">Started</dt>
              <dd className="text-sm text-gray-900">
                {formatDate(subscription.created_at)}
              </dd>
            </div>

            {subscription.current_period_start && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Current Period</dt>
                <dd className="text-sm text-gray-900">
                  {formatDate(subscription.current_period_start)} - {formatDate(subscription.current_period_end)}
                </dd>
              </div>
            )}

            {subscription.next_billing_date && subscription.status === 'active' && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Next Billing</dt>
                <dd className="text-sm text-gray-900">
                  {formatDate(subscription.next_billing_date)}
                </dd>
              </div>
            )}

            {subscription.trial_end && new Date(subscription.trial_end) > new Date() && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Trial Ends</dt>
                <dd className="text-sm text-gray-900">
                  {formatDate(subscription.trial_end)}
                </dd>
              </div>
            )}

            {subscription.canceled_at && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Canceled</dt>
                <dd className="text-sm text-gray-900">
                  {formatDate(subscription.canceled_at)}
                </dd>
              </div>
            )}
          </div>

          {subscription.subscription_product?.features && (
            <div className="mt-4">
              <dt className="text-sm font-medium text-gray-500 mb-2">Features</dt>
              <ul className="space-y-1">
                {subscription.subscription_product.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-600">
                    <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {subscription.status === 'active' && (
          <div className="ml-6">
            <button
              onClick={onCancel}
              disabled={isProcessing}
              className="btn-danger"
            >
              Cancel Subscription
            </button>
          </div>
        )}
      </div>

      {subscription.status === 'past_due' && (
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Payment Past Due
              </h3>
              <p className="mt-1 text-sm text-yellow-700">
                Your subscription payment is past due. Please update your payment method or contact support.
              </p>
            </div>
          </div>
        </div>
      )}

      {subscription.status === 'canceled' && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex">
            <XCircleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Subscription Canceled
              </h3>
              <p className="mt-1 text-sm text-red-700">
                Your subscription has been canceled. You'll continue to have access until the end of your billing period.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionCard;
