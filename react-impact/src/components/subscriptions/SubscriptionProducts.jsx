import React, { useState, useEffect } from 'react';
import { subscriptionProductsAPI, paymentMethodsAPI, subscriptionsAPI } from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';
import { 
  CheckIcon,
  CreditCardIcon,
  ExclamationTriangleIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

const SubscriptionProducts = () => {
  const [loading, setLoading] = useState(true);
  const [subscribing, setSubscribing] = useState(null);
  const [data, setData] = useState({
    products: [],
    paymentMethods: [],
    currentSubscription: null
  });
  const [alerts, setAlerts] = useState([]);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [productsRes, paymentMethodsRes, subscriptionsRes] = await Promise.all([
        subscriptionProductsAPI.getAll(),
        paymentMethodsAPI.getAll(),
        subscriptionsAPI.getAll()
      ]);

      setData({
        products: productsRes.data.data || [],
        paymentMethods: paymentMethodsRes.data.data || [],
        currentSubscription: subscriptionsRes.data.data?.[0] || null
      });
    } catch (error) {
      console.error('Failed to fetch data:', error);
      addAlert('Failed to load subscription products', 'error');
    } finally {
      setLoading(false);
    }
  };

  const addAlert = (message, type = 'info') => {
    const alert = { id: Date.now(), message, type };
    setAlerts(prev => [...prev, alert]);
    setTimeout(() => {
      setAlerts(prev => prev.filter(a => a.id !== alert.id));
    }, 5000);
  };

  const handleSubscribe = async (productId) => {
    // Check if user has payment methods
    if (data.paymentMethods.length === 0) {
      addAlert('Please add a payment method before subscribing', 'error');
      return;
    }

    // Get default payment method
    const defaultPaymentMethod = data.paymentMethods.find(pm => pm.is_default);
    if (!defaultPaymentMethod) {
      addAlert('Please set a default payment method before subscribing', 'error');
      return;
    }

    setSubscribing(productId);
    try {
      const response = await subscriptionsAPI.create({
        subscription_product_id: productId,
        payment_method_id: defaultPaymentMethod.id
      });

      if (response.data.success) {
        addAlert('Successfully subscribed!', 'success');
        await fetchData(); // Refresh data
      }
    } catch (error) {
      console.error('Failed to subscribe:', error);
      addAlert(error.response?.data?.message || 'Failed to subscribe', 'error');
    } finally {
      setSubscribing(null);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getPopularBadge = (product) => {
    // Mark the middle-priced product as popular
    const sortedProducts = [...data.products].sort((a, b) => a.price - b.price);
    const middleIndex = Math.floor(sortedProducts.length / 2);
    return sortedProducts[middleIndex]?.id === product.id;
  };

  const isCurrentPlan = (productId) => {
    return data.currentSubscription?.subscription_product_id === productId;
  };

  const getAlertIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckIcon className="h-5 w-5 text-green-400" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />;
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-blue-400" />;
    }
  };

  const getAlertColors = (type) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900">Choose Your Plan</h1>
        <p className="mt-4 text-lg text-gray-600">
          Select the perfect plan for your needs. Upgrade or downgrade anytime.
        </p>
      </div>

      {/* Alerts */}
      {alerts.map((alert) => (
        <div key={alert.id} className={`border rounded-md p-4 ${getAlertColors(alert.type)}`}>
          <div className="flex">
            {getAlertIcon(alert.type)}
            <div className="ml-3">
              <p className="text-sm font-medium">{alert.message}</p>
            </div>
          </div>
        </div>
      ))}

      {/* Payment Method Warning */}
      {data.paymentMethods.length === 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Payment Method Required
              </h3>
              <p className="mt-1 text-sm text-yellow-700">
                You need to add a payment method before you can subscribe to a plan.
                <a href="/payment-methods" className="font-medium underline ml-1">
                  Add payment method
                </a>
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Current Subscription Info */}
      {data.currentSubscription && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex">
            <CheckIcon className="h-5 w-5 text-blue-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Current Subscription
              </h3>
              <p className="mt-1 text-sm text-blue-700">
                You are currently subscribed to the{' '}
                <span className="font-medium">
                  {data.products.find(p => p.id === data.currentSubscription.subscription_product_id)?.name}
                </span>{' '}
                plan. You can upgrade to a higher tier plan below.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {data.products.map((product) => {
          const isPopular = getPopularBadge(product);
          const isCurrent = isCurrentPlan(product.id);
          const isSubscribing = subscribing === product.id;

          return (
            <div
              key={product.id}
              className={`relative rounded-lg border-2 p-6 ${
                isPopular
                  ? 'border-primary-500 shadow-lg'
                  : isCurrent
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200'
              }`}
            >
              {/* Popular Badge */}
              {isPopular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-600 text-white">
                    <StarIconSolid className="h-3 w-3 mr-1" />
                    Most Popular
                  </span>
                </div>
              )}

              {/* Current Plan Badge */}
              {isCurrent && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-600 text-white">
                    <CheckIcon className="h-3 w-3 mr-1" />
                    Current Plan
                  </span>
                </div>
              )}

              <div className="text-center">
                <h3 className="text-xl font-semibold text-gray-900">{product.name}</h3>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-gray-900">
                    {formatCurrency(product.price)}
                  </span>
                  <span className="text-gray-500">/{product.billing_cycle}</span>
                </div>
                <p className="mt-4 text-gray-600">{product.description}</p>
              </div>

              {/* Features */}
              {product.features && product.features.length > 0 && (
                <div className="mt-6">
                  <ul className="space-y-3">
                    {product.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <CheckIcon className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span className="ml-3 text-sm text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Action Button */}
              <div className="mt-8">
                {isCurrent ? (
                  <button
                    disabled
                    className="w-full bg-green-100 text-green-800 font-medium py-2 px-4 rounded-lg cursor-not-allowed"
                  >
                    Current Plan
                  </button>
                ) : (
                  <button
                    onClick={() => handleSubscribe(product.id)}
                    disabled={isSubscribing || data.paymentMethods.length === 0}
                    className={`w-full font-medium py-2 px-4 rounded-lg transition-colors duration-200 ${
                      isPopular
                        ? 'btn-primary'
                        : 'btn-secondary'
                    }`}
                  >
                    {isSubscribing ? (
                      <LoadingSpinner size="sm" />
                    ) : data.currentSubscription ? (
                      'Upgrade to This Plan'
                    ) : (
                      'Subscribe Now'
                    )}
                  </button>
                )}
              </div>

              {/* Payment Method Info */}
              {data.paymentMethods.length > 0 && !isCurrent && (
                <div className="mt-4 text-center">
                  <div className="flex items-center justify-center text-xs text-gray-500">
                    <CreditCardIcon className="h-4 w-4 mr-1" />
                    {data.paymentMethods.find(pm => pm.is_default)?.brand?.toUpperCase()} 
                    ****{data.paymentMethods.find(pm => pm.is_default)?.last_four}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* No Products */}
      {data.products.length === 0 && (
        <div className="text-center py-12">
          <StarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No subscription plans available</h3>
          <p className="mt-1 text-sm text-gray-500">
            Check back later for available subscription plans.
          </p>
        </div>
      )}
    </div>
  );
};

export default SubscriptionProducts;
