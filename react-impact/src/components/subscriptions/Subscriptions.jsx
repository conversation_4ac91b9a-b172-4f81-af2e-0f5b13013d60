import React, { useState, useEffect } from 'react';
import { subscriptionProductsAPI, subscriptionsAPI, refundsAPI } from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';
import SubscriptionCard from './SubscriptionCard';
import UpgradeModal from './UpgradeModal';
import CancelModal from './CancelModal';
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

const Subscriptions = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState({
    currentSubscription: null,
    availablePlans: [],
    isProcessing: false
  });
  const [modals, setModals] = useState({
    upgrade: { open: false, plan: null },
    cancel: { open: false }
  });
  const [alerts, setAlerts] = useState([]);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [subscriptionsRes, productsRes] = await Promise.all([
        subscriptionsAPI.getAll(),
        subscriptionProductsAPI.getAll()
      ]);

      setData({
        currentSubscription: subscriptionsRes.data.data?.[0] || null,
        availablePlans: productsRes.data.data || [],
        isProcessing: false
      });
    } catch (error) {
      console.error('Failed to fetch subscription data:', error);
      addAlert('Failed to load subscription data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const addAlert = (message, type = 'info') => {
    const alert = { id: Date.now(), message, type };
    setAlerts(prev => [...prev, alert]);
    setTimeout(() => {
      setAlerts(prev => prev.filter(a => a.id !== alert.id));
    }, 5000);
  };

  const handleUpgrade = async (planId, paymentMethodId) => {
    setData(prev => ({ ...prev, isProcessing: true }));
    
    try {
      // Calculate pro-rated refund first
      const refundRes = await refundsAPI.calculateProrated({
        current_subscription_id: data.currentSubscription.id,
        new_product_id: planId
      });

      // Create new subscription
      const subscriptionRes = await subscriptionsAPI.create({
        subscription_product_id: planId,
        payment_method_id: paymentMethodId,
        prorate: true
      });

      if (subscriptionRes.data.success) {
        addAlert('Subscription upgraded successfully!', 'success');
        await fetchData();
        setModals(prev => ({ ...prev, upgrade: { open: false, plan: null } }));
      }
    } catch (error) {
      console.error('Upgrade failed:', error);
      addAlert(error.response?.data?.message || 'Failed to upgrade subscription', 'error');
    } finally {
      setData(prev => ({ ...prev, isProcessing: false }));
    }
  };

  const handleCancel = async (reason) => {
    setData(prev => ({ ...prev, isProcessing: true }));
    
    try {
      const response = await subscriptionsAPI.cancel(data.currentSubscription.id);
      
      if (response.data.success) {
        addAlert('Subscription canceled successfully. Refund will be processed.', 'success');
        await fetchData();
        setModals(prev => ({ ...prev, cancel: { open: false } }));
      }
    } catch (error) {
      console.error('Cancellation failed:', error);
      addAlert(error.response?.data?.message || 'Failed to cancel subscription', 'error');
    } finally {
      setData(prev => ({ ...prev, isProcessing: false }));
    }
  };

  const openUpgradeModal = (plan) => {
    setModals(prev => ({ ...prev, upgrade: { open: true, plan } }));
  };

  const openCancelModal = () => {
    setModals(prev => ({ ...prev, cancel: { open: true } }));
  };

  const closeModals = () => {
    setModals({ upgrade: { open: false, plan: null }, cancel: { open: false } });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const getAlertIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-400" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />;
      default:
        return <InformationCircleIcon className="h-5 w-5 text-blue-400" />;
    }
  };

  const getAlertColors = (type) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Subscription Management</h1>
        <p className="text-gray-600">
          Manage your subscription plan and billing preferences.
        </p>
      </div>

      {/* Alerts */}
      {alerts.map((alert) => (
        <div key={alert.id} className={`border rounded-md p-4 ${getAlertColors(alert.type)}`}>
          <div className="flex">
            {getAlertIcon(alert.type)}
            <div className="ml-3">
              <p className="text-sm font-medium">{alert.message}</p>
            </div>
          </div>
        </div>
      ))}

      {/* Current Subscription */}
      {data.currentSubscription && (
        <div className="card">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Current Subscription</h2>
          <SubscriptionCard 
            subscription={data.currentSubscription}
            onCancel={openCancelModal}
            isProcessing={data.isProcessing}
          />
        </div>
      )}

      {/* Available Plans */}
      <div className="card">
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          {data.currentSubscription ? 'Upgrade Options' : 'Available Plans'}
        </h2>
        
        {data.availablePlans.length === 0 ? (
          <p className="text-gray-500">No subscription plans available.</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {data.availablePlans.map((plan) => {
              const isCurrent = data.currentSubscription?.subscription_product_id === plan.id;
              const canUpgrade = data.currentSubscription && 
                               plan.price > data.currentSubscription.subscription_product?.price &&
                               !isCurrent;
              
              return (
                <div
                  key={plan.id}
                  className={`border rounded-lg p-6 ${
                    isCurrent ? 'border-primary-200 bg-primary-50' : 'border-gray-200'
                  }`}
                >
                  <div className="text-center">
                    <h3 className="text-lg font-medium text-gray-900">{plan.name}</h3>
                    <p className="text-gray-600 mt-2">{plan.description}</p>
                    <div className="mt-4">
                      <span className="text-3xl font-bold text-gray-900">
                        ${plan.price}
                      </span>
                      <span className="text-gray-600">/{plan.billing_interval}</span>
                    </div>
                    
                    {plan.features && (
                      <ul className="mt-4 space-y-2 text-sm text-gray-600">
                        {plan.features.map((feature, index) => (
                          <li key={index} className="flex items-center">
                            <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    )}
                    
                    <div className="mt-6">
                      {isCurrent ? (
                        <span className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100">
                          Current Plan
                        </span>
                      ) : canUpgrade ? (
                        <button
                          onClick={() => openUpgradeModal(plan)}
                          disabled={data.isProcessing}
                          className="btn-primary w-full"
                        >
                          Upgrade to {plan.name}
                        </button>
                      ) : !data.currentSubscription ? (
                        <button
                          onClick={() => openUpgradeModal(plan)}
                          disabled={data.isProcessing}
                          className="btn-primary w-full"
                        >
                          Subscribe
                        </button>
                      ) : (
                        <span className="text-sm text-gray-500">
                          Contact support for downgrades
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Modals */}
      <UpgradeModal
        isOpen={modals.upgrade.open}
        onClose={closeModals}
        plan={modals.upgrade.plan}
        currentSubscription={data.currentSubscription}
        onUpgrade={handleUpgrade}
        isProcessing={data.isProcessing}
      />

      <CancelModal
        isOpen={modals.cancel.open}
        onClose={closeModals}
        subscription={data.currentSubscription}
        onCancel={handleCancel}
        isProcessing={data.isProcessing}
      />
    </div>
  );
};

export default Subscriptions;
