import React, { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon, CreditCardIcon } from '@heroicons/react/24/outline';
import { paymentMethodsAPI, refundsAPI } from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';

const UpgradeModal = ({ isOpen, onClose, plan, currentSubscription, onUpgrade, isProcessing }) => {
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
  const [proratedCalculation, setProratedCalculation] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen && plan) {
      fetchPaymentMethods();
      if (currentSubscription) {
        calculateProration();
      }
    }
  }, [isOpen, plan, currentSubscription]);

  const fetchPaymentMethods = async () => {
    try {
      const response = await paymentMethodsAPI.getAll();
      const methods = response.data.data || [];
      setPaymentMethods(methods);
      
      // Auto-select default payment method
      const defaultMethod = methods.find(method => method.is_default);
      if (defaultMethod) {
        setSelectedPaymentMethod(defaultMethod.id);
      } else if (methods.length > 0) {
        setSelectedPaymentMethod(methods[0].id);
      }
    } catch (error) {
      console.error('Failed to fetch payment methods:', error);
      setError('Failed to load payment methods');
    }
  };

  const calculateProration = async () => {
    if (!currentSubscription || !plan) return;

    setLoading(true);
    try {
      const response = await refundsAPI.calculateProrated({
        current_subscription_id: currentSubscription.id,
        new_product_id: plan.id
      });
      setProratedCalculation(response.data.data);
    } catch (error) {
      console.error('Failed to calculate proration:', error);
      setError('Failed to calculate pricing');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedPaymentMethod) {
      setError('Please select a payment method');
      return;
    }
    onUpgrade(plan.id, selectedPaymentMethod);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const isUpgrade = currentSubscription && plan && plan.price > currentSubscription.subscription_product?.price;

  return (
    <Transition show={isOpen} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white text-gray-400 hover:text-gray-500"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title as="h3" className="text-lg font-semibold leading-6 text-gray-900">
                      {isUpgrade ? 'Upgrade Subscription' : 'Subscribe to Plan'}
                    </Dialog.Title>

                    {plan && (
                      <div className="mt-4">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900">{plan.name}</h4>
                          <p className="text-sm text-gray-600 mt-1">{plan.description}</p>
                          <p className="text-lg font-semibold text-gray-900 mt-2">
                            {formatCurrency(plan.price)}/{plan.billing_interval}
                          </p>
                        </div>

                        {loading && (
                          <div className="mt-4 flex justify-center">
                            <LoadingSpinner size="sm" />
                          </div>
                        )}

                        {proratedCalculation && (
                          <div className="mt-4 bg-blue-50 rounded-lg p-4">
                            <h5 className="font-medium text-blue-900">Billing Summary</h5>
                            <div className="mt-2 space-y-1 text-sm">
                              <div className="flex justify-between">
                                <span className="text-blue-700">Refund for unused time:</span>
                                <span className="text-blue-900 font-medium">
                                  {formatCurrency(proratedCalculation.refund_amount)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-blue-700">New plan charge:</span>
                                <span className="text-blue-900 font-medium">
                                  {formatCurrency(proratedCalculation.new_plan_charge)}
                                </span>
                              </div>
                              <div className="border-t border-blue-200 pt-1 mt-2">
                                <div className="flex justify-between font-medium">
                                  <span className="text-blue-700">Total due today:</span>
                                  <span className="text-blue-900">
                                    {formatCurrency(proratedCalculation.amount_due)}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {error && (
                          <div className="mt-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                            {error}
                          </div>
                        )}

                        <form onSubmit={handleSubmit} className="mt-6">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Payment Method
                            </label>
                            {paymentMethods.length === 0 ? (
                              <div className="text-sm text-gray-500">
                                No payment methods available. Please add a payment method first.
                              </div>
                            ) : (
                              <div className="space-y-2">
                                {paymentMethods.map((method) => (
                                  <label
                                    key={method.id}
                                    className="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
                                  >
                                    <input
                                      type="radio"
                                      name="paymentMethod"
                                      value={method.id}
                                      checked={selectedPaymentMethod === method.id}
                                      onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                                    />
                                    <div className="ml-3 flex items-center">
                                      <CreditCardIcon className="h-5 w-5 text-gray-400 mr-2" />
                                      <div>
                                        <div className="text-sm font-medium text-gray-900">
                                          **** **** **** {method.last_four}
                                        </div>
                                        <div className="text-xs text-gray-500">
                                          {method.brand?.toUpperCase()} • Expires {method.exp_month}/{method.exp_year}
                                          {method.is_default && (
                                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-100 text-primary-800">
                                              Default
                                            </span>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  </label>
                                ))}
                              </div>
                            )}
                          </div>

                          <div className="mt-6 flex space-x-3">
                            <button
                              type="button"
                              onClick={onClose}
                              className="btn-secondary flex-1"
                              disabled={isProcessing}
                            >
                              Cancel
                            </button>
                            <button
                              type="submit"
                              disabled={isProcessing || paymentMethods.length === 0 || !selectedPaymentMethod}
                              className="btn-primary flex-1"
                            >
                              {isProcessing ? (
                                <LoadingSpinner size="sm" />
                              ) : (
                                isUpgrade ? 'Upgrade Now' : 'Subscribe Now'
                              )}
                            </button>
                          </div>
                        </form>
                      </div>
                    )}
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default UpgradeModal;
