import React, { createContext, useContext, useEffect, useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { paymentMethodsAPI } from '../services/api';

const StripeContext = createContext();

export const useStripe = () => {
  const context = useContext(StripeContext);
  if (!context) {
    throw new Error('useStripe must be used within a StripeProvider');
  }
  return context;
};

export const StripeProvider = ({ children }) => {
  const [stripePromise, setStripePromise] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const initializeStripe = async () => {
      try {
        // Get Stripe configuration from backend
        const response = await paymentMethodsAPI.getStripeConfig();
        const { publishable_key } = response.data.data;

        if (!publishable_key) {
          throw new Error('Stripe publishable key not found');
        }

        // Load Stripe with the publishable key
        const stripe = loadStripe(publishable_key);
        setStripePromise(stripe);
      } catch (err) {
        console.error('Failed to initialize Stripe:', err);
        setError(err.message || 'Failed to initialize Stripe');
      } finally {
        setLoading(false);
      }
    };

    initializeStripe();
  }, []);

  const value = {
    stripePromise,
    loading,
    error,
  };

  return (
    <StripeContext.Provider value={value}>
      {children}
    </StripeContext.Provider>
  );
};

export default StripeContext;
