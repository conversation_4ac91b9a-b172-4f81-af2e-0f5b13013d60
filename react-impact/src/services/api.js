import axios from 'axios';

// Subscription Products API
export const subscriptionProductsAPI = {
  getAll: () => axios.get('/subscription-products'),
  getById: (id) => axios.get(`/subscription-products/${id}`),
};

// Subscriptions API
export const subscriptionsAPI = {
  getAll: () => axios.get('/subscriptions'),
  getById: (id) => axios.get(`/subscriptions/${id}`),
  create: (data) => axios.post('/subscriptions', data),
  update: (id, data) => axios.put(`/subscriptions/${id}`, data),
  cancel: (id) => axios.delete(`/subscriptions/${id}`),
};

// Payment Methods API
export const paymentMethodsAPI = {
  getAll: () => axios.get('/payment-methods'),
  getById: (id) => axios.get(`/payment-methods/${id}`),
  create: (data) => axios.post('/payment-methods', data),
  update: (id, data) => axios.put(`/payment-methods/${id}`, data),
  delete: (id) => axios.delete(`/payment-methods/${id}`),
  setDefault: (id) => axios.post(`/payment-methods/${id}/set-default`),
  getStripeConfig: () => axios.get('/payment-methods/stripe-config'),
  createSetupIntent: () => axios.post('/payment-methods/setup-intent'),
  createCheckoutSession: () => axios.post('/payment-methods/checkout-session'),
};

// Payments API
export const paymentsAPI = {
  getAll: () => axios.get('/payments'),
  getById: (id) => axios.get(`/payments/${id}`),
  createIntent: (data) => axios.post('/payments/create-intent', data),
  confirm: (data) => axios.post('/payments/confirm', data),
  retry: (id) => axios.post(`/payments/${id}/retry`),
};

// Invoices API
export const invoicesAPI = {
  getAll: () => axios.get('/invoices'),
  getById: (id) => axios.get(`/invoices/${id}`),
  generate: (data) => axios.post('/invoices/generate', data),
  download: (id) => axios.get(`/invoices/${id}/download`, { responseType: 'blob' }),
  pay: (id, data) => axios.post(`/invoices/${id}/pay`, data),
};

// Payment History API
export const paymentHistoryAPI = {
  getAll: () => axios.get('/payment-history'),
  getStatistics: () => axios.get('/payment-history/statistics'),
  getRecent: () => axios.get('/payment-history/recent'),
  export: () => axios.get('/payment-history/export', { responseType: 'blob' }),
};

// Refunds API
export const refundsAPI = {
  getAll: () => axios.get('/refunds'),
  getById: (id) => axios.get(`/refunds/${id}`),
  getStatistics: () => axios.get('/refunds/statistics'),
  request: (data) => axios.post('/refunds/request', data),
  calculateProrated: (data) => axios.post('/refunds/calculate-prorated', data),
};

// Notifications API
export const notificationsAPI = {
  getAll: () => axios.get('/notifications'),
  getUnreadCount: () => axios.get('/notifications/unread-count'),
  getStatistics: () => axios.get('/notifications/statistics'),
  markAsRead: (id) => axios.post(`/notifications/${id}/mark-as-read`),
  markAllAsRead: () => axios.post('/notifications/mark-all-as-read'),
  delete: (id) => axios.delete(`/notifications/${id}`),
};

// Account Status API
export const accountStatusAPI = {
  getStatus: () => axios.get('/account/status'),
};

// Profile API
export const profileAPI = {
  get: () => axios.get('/profile'),
  update: (data) => axios.put('/profile', data),
};

// User API
export const userAPI = {
  updateProfile: (data) => axios.put('/user/profile', data),
  updatePassword: (data) => axios.put('/user/password', data),
  getProfile: () => axios.get('/user/profile'),
};

// General Settings API
export const generalSettingsAPI = {
  get: () => axios.get('/general-settings'),
};
