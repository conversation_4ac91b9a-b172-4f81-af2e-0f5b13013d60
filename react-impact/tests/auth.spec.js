import { test, expect } from '@playwright/test';

// Mock user data
const mockUser = {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  email_verified_at: '2024-01-01T00:00:00Z',
  created_at: '2024-01-01T00:00:00Z'
};

const mockAuthToken = 'mock-auth-token-12345';

test.describe('Authentication', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing auth state
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
  });

  test.describe('Login Flow', () => {
    test.beforeEach(async ({ page }) => {
      // Mock login API
      await page.route('**/login', async route => {
        const postData = route.request().postDataJSON();
        
        if (postData.email === '<EMAIL>' && postData.password === 'password123') {
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({
              user: mockUser,
              token: mockAuthToken
            })
          });
        } else {
          await route.fulfill({
            status: 422,
            contentType: 'application/json',
            body: JSON.stringify({
              message: 'The provided credentials are incorrect.',
              errors: {
                email: ['The provided credentials are incorrect.']
              }
            })
          });
        }
      });

      await page.goto('/login');
    });

    test('should display login form', async ({ page }) => {
      await expect(page.locator('h2')).toContainText('Sign in to your account');
      await expect(page.locator('input[type="email"]')).toBeVisible();
      await expect(page.locator('input[type="password"]')).toBeVisible();
      await expect(page.locator('button[type="submit"]')).toContainText('Sign in');
      await expect(page.locator('text=Don\'t have an account?')).toBeVisible();
      await expect(page.locator('a[href="/register"]')).toContainText('Sign up');
    });

    test('should login successfully with valid credentials', async ({ page }) => {
      // Fill in login form
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'password123');
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Should redirect to dashboard
      await expect(page).toHaveURL('/dashboard');
      
      // Should show user info in navigation
      await expect(page.locator('text=John Doe')).toBeVisible();
    });

    test('should show error with invalid credentials', async ({ page }) => {
      // Fill in login form with wrong credentials
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'wrongpassword');
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Should show error message
      await expect(page.locator('text=The provided credentials are incorrect.')).toBeVisible();
      
      // Should stay on login page
      await expect(page).toHaveURL('/login');
    });

    test('should show validation errors for empty fields', async ({ page }) => {
      // Submit form without filling fields
      await page.click('button[type="submit"]');
      
      // Should show validation errors
      await expect(page.locator('text=Email is required')).toBeVisible();
      await expect(page.locator('text=Password is required')).toBeVisible();
    });

    test('should show loading state during login', async ({ page }) => {
      // Fill in form
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'password123');
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Should show loading spinner
      await expect(page.locator('button[type="submit"] [data-testid="loading-spinner"]')).toBeVisible();
    });

    test('should navigate to register page', async ({ page }) => {
      // Click sign up link
      await page.click('a[href="/register"]');
      
      // Should navigate to register page
      await expect(page).toHaveURL('/register');
    });
  });

  test.describe('Register Flow', () => {
    test.beforeEach(async ({ page }) => {
      // Mock register API
      await page.route('**/register', async route => {
        const postData = route.request().postDataJSON();
        
        if (postData.email === '<EMAIL>') {
          await route.fulfill({
            status: 422,
            contentType: 'application/json',
            body: JSON.stringify({
              message: 'The email has already been taken.',
              errors: {
                email: ['The email has already been taken.']
              }
            })
          });
        } else {
          await route.fulfill({
            status: 201,
            contentType: 'application/json',
            body: JSON.stringify({
              user: {
                ...mockUser,
                name: postData.name,
                email: postData.email
              },
              token: mockAuthToken
            })
          });
        }
      });

      await page.goto('/register');
    });

    test('should display register form', async ({ page }) => {
      await expect(page.locator('h2')).toContainText('Create your account');
      await expect(page.locator('input[name="name"]')).toBeVisible();
      await expect(page.locator('input[type="email"]')).toBeVisible();
      await expect(page.locator('input[type="password"]')).toBeVisible();
      await expect(page.locator('input[name="password_confirmation"]')).toBeVisible();
      await expect(page.locator('button[type="submit"]')).toContainText('Sign up');
      await expect(page.locator('text=Already have an account?')).toBeVisible();
      await expect(page.locator('a[href="/login"]')).toContainText('Sign in');
    });

    test('should register successfully with valid data', async ({ page }) => {
      // Fill in register form
      await page.fill('input[name="name"]', 'Jane Doe');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'password123');
      await page.fill('input[name="password_confirmation"]', 'password123');
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Should redirect to dashboard
      await expect(page).toHaveURL('/dashboard');
      
      // Should show user info in navigation
      await expect(page.locator('text=Jane Doe')).toBeVisible();
    });

    test('should show error for existing email', async ({ page }) => {
      // Fill in form with existing email
      await page.fill('input[name="name"]', 'Jane Doe');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'password123');
      await page.fill('input[name="password_confirmation"]', 'password123');
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Should show error message
      await expect(page.locator('text=The email has already been taken.')).toBeVisible();
      
      // Should stay on register page
      await expect(page).toHaveURL('/register');
    });

    test('should validate password confirmation', async ({ page }) => {
      // Fill in form with mismatched passwords
      await page.fill('input[name="name"]', 'Jane Doe');
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'password123');
      await page.fill('input[name="password_confirmation"]', 'differentpassword');
      
      // Submit form
      await page.click('button[type="submit"]');
      
      // Should show validation error
      await expect(page.locator('text=Passwords do not match')).toBeVisible();
    });

    test('should navigate to login page', async ({ page }) => {
      // Click sign in link
      await page.click('a[href="/login"]');
      
      // Should navigate to login page
      await expect(page).toHaveURL('/login');
    });
  });

  test.describe('Protected Routes', () => {
    test('should redirect to login when accessing protected route without auth', async ({ page }) => {
      // Try to access dashboard without authentication
      await page.goto('/dashboard');
      
      // Should redirect to login
      await expect(page).toHaveURL('/login');
    });

    test('should redirect to login when accessing subscriptions without auth', async ({ page }) => {
      // Try to access subscriptions without authentication
      await page.goto('/subscriptions');
      
      // Should redirect to login
      await expect(page).toHaveURL('/login');
    });

    test('should redirect to login when accessing profile without auth', async ({ page }) => {
      // Try to access profile without authentication
      await page.goto('/profile');
      
      // Should redirect to login
      await expect(page).toHaveURL('/login');
    });

    test('should allow access to protected routes when authenticated', async ({ page }) => {
      // Mock authenticated user API
      await page.route('**/user', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockUser)
        });
      });

      // Set auth token in localStorage
      await page.goto('/login');
      await page.evaluate((token) => {
        localStorage.setItem('auth_token', token);
      }, mockAuthToken);

      // Try to access dashboard
      await page.goto('/dashboard');
      
      // Should stay on dashboard
      await expect(page).toHaveURL('/dashboard');
      await expect(page.locator('text=Dashboard')).toBeVisible();
    });
  });

  test.describe('Logout Flow', () => {
    test.beforeEach(async ({ page }) => {
      // Mock logout API
      await page.route('**/logout', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ message: 'Successfully logged out' })
        });
      });

      // Mock authenticated user API
      await page.route('**/user', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockUser)
        });
      });

      // Set up authenticated state
      await page.goto('/login');
      await page.evaluate((token) => {
        localStorage.setItem('auth_token', token);
      }, mockAuthToken);
      
      await page.goto('/dashboard');
    });

    test('should logout successfully', async ({ page }) => {
      // Click user menu
      await page.click('button:has-text("John Doe")');
      
      // Click logout
      await page.click('text=Sign out');
      
      // Should redirect to login page
      await expect(page).toHaveURL('/login');
      
      // Should clear auth token
      const token = await page.evaluate(() => localStorage.getItem('auth_token'));
      expect(token).toBeNull();
    });

    test('should show logout confirmation', async ({ page }) => {
      // Click user menu
      await page.click('button:has-text("John Doe")');
      
      // Should show logout option
      await expect(page.locator('text=Sign out')).toBeVisible();
    });
  });

  test.describe('Auth State Persistence', () => {
    test('should persist auth state on page reload', async ({ page }) => {
      // Mock authenticated user API
      await page.route('**/user', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockUser)
        });
      });

      // Set auth token
      await page.goto('/login');
      await page.evaluate((token) => {
        localStorage.setItem('auth_token', token);
      }, mockAuthToken);

      // Navigate to dashboard
      await page.goto('/dashboard');
      await expect(page.locator('text=John Doe')).toBeVisible();

      // Reload page
      await page.reload();

      // Should still be authenticated
      await expect(page.locator('text=John Doe')).toBeVisible();
      await expect(page).toHaveURL('/dashboard');
    });

    test('should handle invalid token gracefully', async ({ page }) => {
      // Mock 401 response for invalid token
      await page.route('**/user', async route => {
        await route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({ message: 'Unauthenticated' })
        });
      });

      // Set invalid auth token
      await page.goto('/login');
      await page.evaluate(() => {
        localStorage.setItem('auth_token', 'invalid-token');
      });

      // Try to access dashboard
      await page.goto('/dashboard');

      // Should redirect to login and clear invalid token
      await expect(page).toHaveURL('/login');
      
      const token = await page.evaluate(() => localStorage.getItem('auth_token'));
      expect(token).toBeNull();
    });
  });
});
