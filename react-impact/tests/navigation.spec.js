import { test, expect } from '@playwright/test';

// Mock user data
const mockUser = {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  email_verified_at: '2024-01-01T00:00:00Z',
  created_at: '2024-01-01T00:00:00Z'
};

const mockAuthToken = 'mock-auth-token-12345';

test.describe('Navigation and Layout', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authenticated user API
    await page.route('**/user', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockUser)
      });
    });

    // Mock logout API
    await page.route('**/logout', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ message: 'Successfully logged out' })
      });
    });

    // Set up authenticated state
    await page.goto('/login');
    await page.evaluate((token) => {
      localStorage.setItem('auth_token', token);
    }, mockAuthToken);
    
    await page.goto('/dashboard');
  });

  test.describe('Sidebar Navigation', () => {
    test('should display all navigation items', async ({ page }) => {
      // Check that all main navigation items are present
      await expect(page.locator('nav a[href="/dashboard"]')).toContainText('Dashboard');
      await expect(page.locator('nav a[href="/subscriptions"]')).toContainText('Subscriptions');
      await expect(page.locator('nav a[href="/subscription-products"]')).toContainText('Subscription Plans');
      await expect(page.locator('nav a[href="/payment-methods"]')).toContainText('Payment Methods');
      await expect(page.locator('nav a[href="/profile"]')).toContainText('Profile');
    });

    test('should highlight active navigation item', async ({ page }) => {
      // Dashboard should be active by default
      const dashboardLink = page.locator('nav a[href="/dashboard"]');
      await expect(dashboardLink).toHaveClass(/bg-primary|bg-blue/);
      
      // Navigate to subscriptions
      await page.click('nav a[href="/subscriptions"]');
      await expect(page).toHaveURL('/subscriptions');
      
      // Subscriptions should now be active
      const subscriptionsLink = page.locator('nav a[href="/subscriptions"]');
      await expect(subscriptionsLink).toHaveClass(/bg-primary|bg-blue/);
      
      // Dashboard should no longer be active
      await expect(dashboardLink).not.toHaveClass(/bg-primary|bg-blue/);
    });

    test('should navigate to different pages', async ({ page }) => {
      // Test navigation to each page
      const navigationTests = [
        { href: '/subscriptions', text: 'Subscriptions' },
        { href: '/subscription-products', text: 'Subscription Plans' },
        { href: '/payment-methods', text: 'Payment Methods' },
        { href: '/profile', text: 'Profile' },
        { href: '/dashboard', text: 'Dashboard' }
      ];

      for (const nav of navigationTests) {
        await page.click(`nav a[href="${nav.href}"]`);
        await expect(page).toHaveURL(nav.href);
        
        // Verify page content loads
        if (nav.href === '/dashboard') {
          await expect(page.locator('h1, h2, h3')).toContainText('Dashboard');
        } else if (nav.href === '/subscriptions') {
          await expect(page.locator('h1, h2, h3')).toContainText('Subscription');
        } else if (nav.href === '/subscription-products') {
          await expect(page.locator('h1, h2, h3')).toContainText('Plan');
        } else if (nav.href === '/payment-methods') {
          await expect(page.locator('h1, h2, h3')).toContainText('Payment');
        } else if (nav.href === '/profile') {
          await expect(page.locator('h1, h2, h3')).toContainText('Profile');
        }
      }
    });

    test('should show navigation icons', async ({ page }) => {
      // Check that navigation items have icons
      await expect(page.locator('nav a[href="/dashboard"] svg')).toBeVisible();
      await expect(page.locator('nav a[href="/subscriptions"] svg')).toBeVisible();
      await expect(page.locator('nav a[href="/subscription-products"] svg')).toBeVisible();
      await expect(page.locator('nav a[href="/payment-methods"] svg')).toBeVisible();
      await expect(page.locator('nav a[href="/profile"] svg')).toBeVisible();
    });

    test('should collapse sidebar on mobile', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Sidebar should be hidden on mobile by default
      const sidebar = page.locator('nav[aria-label="Sidebar"]');
      await expect(sidebar).toHaveClass(/hidden|md:flex/);
      
      // Mobile menu button should be visible
      const mobileMenuButton = page.locator('button[aria-label="Open sidebar"]');
      await expect(mobileMenuButton).toBeVisible();
      
      // Click mobile menu button
      await mobileMenuButton.click();
      
      // Sidebar should now be visible
      await expect(sidebar).toBeVisible();
    });
  });

  test.describe('Top Navigation Bar', () => {
    test('should display user information', async ({ page }) => {
      // Should show user name
      await expect(page.locator('text=John Doe')).toBeVisible();
      
      // Should show user email in dropdown
      await page.click('button:has-text("John Doe")');
      await expect(page.locator('text=<EMAIL>')).toBeVisible();
    });

    test('should show user dropdown menu', async ({ page }) => {
      // Click user menu button
      await page.click('button:has-text("John Doe")');
      
      // Should show dropdown menu items
      await expect(page.locator('text=Your Profile')).toBeVisible();
      await expect(page.locator('text=Settings')).toBeVisible();
      await expect(page.locator('text=Sign out')).toBeVisible();
    });

    test('should navigate to profile from dropdown', async ({ page }) => {
      // Click user menu button
      await page.click('button:has-text("John Doe")');
      
      // Click profile link
      await page.click('text=Your Profile');
      
      // Should navigate to profile page
      await expect(page).toHaveURL('/profile');
    });

    test('should logout from dropdown', async ({ page }) => {
      // Click user menu button
      await page.click('button:has-text("John Doe")');
      
      // Click sign out
      await page.click('text=Sign out');
      
      // Should redirect to login page
      await expect(page).toHaveURL('/login');
      
      // Should clear auth token
      const token = await page.evaluate(() => localStorage.getItem('auth_token'));
      expect(token).toBeNull();
    });

    test('should show application logo/title', async ({ page }) => {
      // Should show application branding
      await expect(page.locator('text=ImpactIntels')).toBeVisible();
    });

    test('should close dropdown when clicking outside', async ({ page }) => {
      // Open dropdown
      await page.click('button:has-text("John Doe")');
      await expect(page.locator('text=Your Profile')).toBeVisible();
      
      // Click outside dropdown
      await page.click('main');
      
      // Dropdown should close
      await expect(page.locator('text=Your Profile')).not.toBeVisible();
    });
  });

  test.describe('Responsive Layout', () => {
    test('should adapt to tablet viewport', async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // Navigation should still be visible
      await expect(page.locator('nav a[href="/dashboard"]')).toBeVisible();
      
      // Content should adjust properly
      const main = page.locator('main');
      await expect(main).toBeVisible();
    });

    test('should adapt to desktop viewport', async ({ page }) => {
      // Set large desktop viewport
      await page.setViewportSize({ width: 1920, height: 1080 });
      
      // Sidebar should be fully expanded
      await expect(page.locator('nav a[href="/dashboard"]')).toBeVisible();
      
      // Content should have proper spacing
      const main = page.locator('main');
      await expect(main).toBeVisible();
    });

    test('should handle very small mobile viewport', async ({ page }) => {
      // Set very small viewport
      await page.setViewportSize({ width: 320, height: 568 });
      
      // Mobile menu should be accessible
      const mobileMenuButton = page.locator('button[aria-label="Open sidebar"]');
      await expect(mobileMenuButton).toBeVisible();
      
      // Content should not overflow
      const body = page.locator('body');
      const bodyBox = await body.boundingBox();
      expect(bodyBox.width).toBeLessThanOrEqual(320);
    });
  });

  test.describe('Accessibility', () => {
    test('should have proper ARIA labels', async ({ page }) => {
      // Check sidebar ARIA label
      await expect(page.locator('nav[aria-label="Sidebar"]')).toBeVisible();
      
      // Check mobile menu button ARIA label
      await page.setViewportSize({ width: 375, height: 667 });
      await expect(page.locator('button[aria-label="Open sidebar"]')).toBeVisible();
    });

    test('should support keyboard navigation', async ({ page }) => {
      // Focus on first navigation item
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab'); // Skip to navigation
      
      // Should be able to navigate with arrow keys
      await page.keyboard.press('ArrowDown');
      await page.keyboard.press('Enter');
      
      // Should navigate to the focused item
      await expect(page).toHaveURL('/subscriptions');
    });

    test('should have proper focus indicators', async ({ page }) => {
      // Tab to navigation items
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      
      // Check that focused element has visible focus indicator
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
      
      // Focus indicator should be visible (outline or ring)
      const focusedBox = await focusedElement.boundingBox();
      expect(focusedBox).toBeTruthy();
    });

    test('should have semantic HTML structure', async ({ page }) => {
      // Check for proper semantic elements
      await expect(page.locator('nav')).toBeVisible();
      await expect(page.locator('main')).toBeVisible();
      await expect(page.locator('header')).toBeVisible();
      
      // Check for proper heading hierarchy
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      await expect(headings.first()).toBeVisible();
    });
  });

  test.describe('Loading States', () => {
    test('should show loading state while fetching user data', async ({ page }) => {
      // Mock slow user API response
      await page.route('**/user', async route => {
        await new Promise(resolve => setTimeout(resolve, 1000));
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockUser)
        });
      });

      // Clear auth state and reload
      await page.evaluate(() => localStorage.clear());
      await page.evaluate((token) => {
        localStorage.setItem('auth_token', token);
      }, mockAuthToken);
      
      await page.reload();
      
      // Should show loading indicator
      await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
    });

    test('should handle user data fetch error', async ({ page }) => {
      // Mock user API error
      await page.route('**/user', async route => {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' })
        });
      });

      await page.reload();
      
      // Should redirect to login on error
      await expect(page).toHaveURL('/login');
    });
  });
});
