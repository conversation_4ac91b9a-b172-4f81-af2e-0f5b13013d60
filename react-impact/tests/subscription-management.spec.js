import { test, expect } from '@playwright/test';

// Mock data
const mockCurrentSubscription = {
  id: 1,
  subscription_product_id: 2,
  status: 'active',
  current_period_end: '2024-02-15T00:00:00Z',
  created_at: '2024-01-15T00:00:00Z'
};

const mockCurrentPlan = {
  id: 2,
  name: 'Pro Plan',
  description: 'Great for growing teams and businesses',
  price: 29.99,
  billing_cycle: 'monthly',
  features: ['Unlimited Projects', '100GB Storage', 'Priority Support', 'Advanced Analytics']
};

const mockAvailablePlans = [
  {
    id: 1,
    name: 'Basic Plan',
    description: 'Perfect for individuals getting started',
    price: 9.99,
    billing_cycle: 'monthly',
    features: ['5 Projects', '10GB Storage', 'Email Support']
  },
  {
    id: 2,
    name: 'Pro Plan',
    description: 'Great for growing teams and businesses',
    price: 29.99,
    billing_cycle: 'monthly',
    features: ['Unlimited Projects', '100GB Storage', 'Priority Support', 'Advanced Analytics']
  },
  {
    id: 3,
    name: 'Enterprise Plan',
    description: 'For large organizations with advanced needs',
    price: 99.99,
    billing_cycle: 'monthly',
    features: ['Unlimited Everything', '1TB Storage', '24/7 Phone Support', 'Custom Integrations']
  }
];

const mockPaymentMethods = [
  {
    id: 1,
    brand: 'visa',
    last_four: '4242',
    exp_month: 12,
    exp_year: 2025,
    is_default: true
  },
  {
    id: 2,
    brand: 'mastercard',
    last_four: '8888',
    exp_month: 6,
    exp_year: 2026,
    is_default: false
  }
];

const mockUpgradeDetails = {
  refund_amount: 15.00,
  new_plan_amount: 99.99,
  amount_due: 84.99
};

test.describe('Subscription Management Page', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API endpoints
    await page.route('**/subscriptions', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ data: [mockCurrentSubscription] })
        });
      } else if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        });
      } else if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        });
      }
    });

    await page.route('**/subscription-products', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ data: mockAvailablePlans })
      });
    });

    await page.route('**/payment-methods', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ data: mockPaymentMethods })
      });
    });

    await page.route('**/refunds/calculate-prorated', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockUpgradeDetails)
      });
    });

    // Navigate to subscriptions page
    await page.goto('/subscriptions');
  });

  test('should display current subscription details', async ({ page }) => {
    // Wait for page to load
    await expect(page.locator('h3:has-text("Pro Plan")')).toBeVisible();
    await expect(page.locator('text=Current Subscription')).toBeVisible();
    
    // Check subscription details
    await expect(page.locator('text=$29.99')).toBeVisible();
    await expect(page.locator('text=/monthly')).toBeVisible();
    await expect(page.locator('text=February 15, 2024')).toBeVisible(); // Next billing date
    
    // Check status
    await expect(page.locator('text=Active')).toBeVisible();
  });

  test('should display plan features', async ({ page }) => {
    // Check that plan features are displayed
    for (const feature of mockCurrentPlan.features) {
      await expect(page.locator(`text=${feature}`)).toBeVisible();
    }
  });

  test('should show subscription details section', async ({ page }) => {
    // Check subscription details section
    await expect(page.locator('text=Subscription Details')).toBeVisible();
    await expect(page.locator('text=Started:')).toBeVisible();
    await expect(page.locator('text=January 15, 2024')).toBeVisible();
    await expect(page.locator('text=Billing Cycle:')).toBeVisible();
    await expect(page.locator('text=monthly')).toBeVisible();
  });

  test('should open upgrade modal when upgrade button clicked', async ({ page }) => {
    // Click upgrade button
    await page.click('button:has-text("Upgrade Plan")');
    
    // Should open upgrade modal
    await expect(page.locator('text=Upgrade Your Subscription')).toBeVisible();
    await expect(page.locator('text=Current Plan')).toBeVisible();
    await expect(page.locator('text=Select New Plan')).toBeVisible();
  });

  test('should show only higher tier plans in upgrade modal', async ({ page }) => {
    // Click upgrade button
    await page.click('button:has-text("Upgrade Plan")');
    
    // Should only show Enterprise plan (higher than current Pro plan)
    await expect(page.locator('text=Enterprise Plan')).toBeVisible();
    
    // Should not show Basic plan (lower tier)
    await expect(page.locator('text=Basic Plan')).not.toBeVisible();
    
    // Should not show current Pro plan
    const modalProPlan = page.locator('[role="dialog"] >> text=Pro Plan');
    await expect(modalProPlan).not.toBeVisible();
  });

  test('should calculate upgrade pricing when plan selected', async ({ page }) => {
    // Click upgrade button
    await page.click('button:has-text("Upgrade Plan")');
    
    // Select Enterprise plan
    await page.click('text=Enterprise Plan');
    
    // Should show calculating message first
    await expect(page.locator('text=Calculating pricing...')).toBeVisible();
    
    // Should show upgrade summary
    await expect(page.locator('text=Upgrade Summary')).toBeVisible();
    await expect(page.locator('text=Prorated refund:')).toBeVisible();
    await expect(page.locator('text=$15.00')).toBeVisible();
    await expect(page.locator('text=New plan charge:')).toBeVisible();
    await expect(page.locator('text=$99.99')).toBeVisible();
    await expect(page.locator('text=Total due today:')).toBeVisible();
    await expect(page.locator('text=$84.99')).toBeVisible();
  });

  test('should show payment method selection in upgrade modal', async ({ page }) => {
    // Click upgrade button
    await page.click('button:has-text("Upgrade Plan")');
    
    // Select Enterprise plan
    await page.click('text=Enterprise Plan');
    
    // Should show payment methods
    await expect(page.locator('text=Payment Method')).toBeVisible();
    await expect(page.locator('text=VISA ****4242')).toBeVisible();
    await expect(page.locator('text=MASTERCARD ****8888')).toBeVisible();
    
    // Default payment method should be selected
    const defaultPaymentMethod = page.locator('text=VISA ****4242').locator('..');
    await expect(defaultPaymentMethod.locator('[class*="bg-primary"]')).toBeVisible();
  });

  test('should complete upgrade process', async ({ page }) => {
    // Click upgrade button
    await page.click('button:has-text("Upgrade Plan")');
    
    // Select Enterprise plan
    await page.click('text=Enterprise Plan');
    
    // Wait for pricing calculation
    await expect(page.locator('text=Upgrade Summary')).toBeVisible();
    
    // Click upgrade button
    await page.click('button:has-text("Upgrade Subscription")');
    
    // Should show loading state
    await expect(page.locator('button:has-text("Upgrade Subscription") [data-testid="loading-spinner"]')).toBeVisible();
    
    // Should show success message and close modal
    await expect(page.locator('text=Successfully upgraded your subscription!')).toBeVisible();
    await expect(page.locator('text=Upgrade Your Subscription')).not.toBeVisible();
  });

  test('should open cancel modal when cancel button clicked', async ({ page }) => {
    // Click cancel button
    await page.click('button:has-text("Cancel Subscription")');
    
    // Should open cancel modal
    await expect(page.locator('text=Cancel Subscription')).toBeVisible();
    await expect(page.locator('text=Are you sure you want to cancel')).toBeVisible();
  });

  test('should show cancellation reasons in cancel modal', async ({ page }) => {
    // Click cancel button
    await page.click('button:has-text("Cancel Subscription")');
    
    // Should show reason options
    await expect(page.locator('text=Too expensive')).toBeVisible();
    await expect(page.locator('text=Not using enough')).toBeVisible();
    await expect(page.locator('text=Missing features')).toBeVisible();
    await expect(page.locator('text=Poor customer service')).toBeVisible();
    await expect(page.locator('text=Other')).toBeVisible();
  });

  test('should complete cancellation process', async ({ page }) => {
    // Click cancel button
    await page.click('button:has-text("Cancel Subscription")');
    
    // Select a reason
    await page.click('text=Too expensive');
    
    // Add feedback
    await page.fill('textarea[placeholder*="feedback"]', 'The pricing is too high for my current needs.');
    
    // Click cancel subscription button
    await page.click('button:has-text("Cancel Subscription")');
    
    // Should show loading state
    await expect(page.locator('button:has-text("Cancel Subscription") [data-testid="loading-spinner"]')).toBeVisible();
    
    // Should show success message
    await expect(page.locator('text=Subscription cancelled successfully')).toBeVisible();
  });

  test('should handle no active subscription state', async ({ page }) => {
    // Mock no subscription
    await page.route('**/subscriptions', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ data: [] })
      });
    });

    await page.reload();

    // Should show no subscription message
    await expect(page.locator('text=No Active Subscription')).toBeVisible();
    await expect(page.locator('text=You don\'t have an active subscription')).toBeVisible();
    await expect(page.locator('text=View Plans')).toBeVisible();
  });

  test('should handle cancelled subscription state', async ({ page }) => {
    // Mock cancelled subscription
    const cancelledSubscription = {
      ...mockCurrentSubscription,
      status: 'cancelled',
      cancelled_at: '2024-01-20T00:00:00Z'
    };

    await page.route('**/subscriptions', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ data: [cancelledSubscription] })
      });
    });

    await page.reload();

    // Should show cancelled state
    await expect(page.locator('text=Subscription Cancelled')).toBeVisible();
    await expect(page.locator('text=Your subscription has been cancelled')).toBeVisible();
    await expect(page.locator('text=Subscribe to a new plan')).toBeVisible();
    
    // Should not show upgrade/cancel buttons
    await expect(page.locator('button:has-text("Upgrade Plan")')).not.toBeVisible();
    await expect(page.locator('button:has-text("Cancel Subscription")')).not.toBeVisible();
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Mock API error for subscriptions
    await page.route('**/subscriptions', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      });
    });

    await page.reload();

    // Should show error message
    await expect(page.locator('text=Failed to load subscription data')).toBeVisible();
  });

  test('should handle upgrade API error', async ({ page }) => {
    // Mock upgrade API error
    await page.route('**/subscriptions/1', async route => {
      if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ message: 'Upgrade failed' })
        });
      }
    });

    // Try to upgrade
    await page.click('button:has-text("Upgrade Plan")');
    await page.click('text=Enterprise Plan');
    await expect(page.locator('text=Upgrade Summary')).toBeVisible();
    await page.click('button:has-text("Upgrade Subscription")');
    
    // Should show error message
    await expect(page.locator('text=Upgrade failed')).toBeVisible();
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Should still display subscription details
    await expect(page.locator('h3:has-text("Pro Plan")')).toBeVisible();
    await expect(page.locator('text=$29.99')).toBeVisible();
    
    // Action buttons should stack vertically on mobile
    const upgradeButton = page.locator('button:has-text("Upgrade Plan")');
    const cancelButton = page.locator('button:has-text("Cancel Subscription")');
    
    const upgradeBox = await upgradeButton.boundingBox();
    const cancelBox = await cancelButton.boundingBox();
    
    // Buttons should be stacked (cancel button below upgrade button)
    expect(cancelBox.y).toBeGreaterThan(upgradeBox.y + upgradeBox.height - 10);
  });
});
