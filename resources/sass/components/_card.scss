.card {
  @apply rounded-md bg-white dark:bg-slate-800;
}
.card-title {
  @apply text-lg  font-medium capitalize leading-[24px] text-slate-900 dark:text-white  md:text-xl md:leading-[28px];
}
.card-subtitle {
  @apply mt-1 text-sm font-medium leading-5 text-slate-600 dark:text-slate-300;
}

.card-header {
  @apply flex items-center justify-between px-6 py-6;
}
.card-header:not(.noborder) {
  @apply border-b border-slate-200 pb-5 dark:border-slate-700;
}

.card-footer {
  @apply flex items-center justify-between border-t border-slate-200 px-6 pt-6 pb-5 dark:border-slate-700;
}

.card-height-auto {
  .card {
    @apply h-min;
  }
}
