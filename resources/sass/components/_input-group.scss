// .input-area:focus-within button,
// .input-area:focus-within span {
//   @apply border-slate-800 outline-transparent ring-transparent transition-all duration-500;
// }
.input-group-control {
  @apply rounded border border-slate-200 bg-white px-3 text-sm text-slate-900 transition duration-300 ease-in-out  placeholder:font-light  
  placeholder:text-slate-400 focus:border-slate-600 focus:outline-none focus:ring-0 dark:border-slate-700  dark:bg-slate-900 dark:text-white  dark:placeholder:text-slate-400 dark:focus:border-slate-900;
}

.fromGroup2 {
  &.has-error {
    .input-group-control {
      @apply border-danger-500 focus:ring-1  focus:ring-danger-500 focus:ring-opacity-90;
    }
  }
  &.is-valid {
    .input-group-control {
      @apply border-success-500 focus:ring-1 focus:ring-success-500 focus:ring-opacity-90;
    }
  }
}

.input-group-control[readonly] {
  @apply bg-slate-200 text-slate-400 dark:bg-slate-600;
}

.input-group-control[disabled] {
  @apply cursor-not-allowed bg-slate-50 text-slate-400 placeholder:text-opacity-60 dark:bg-slate-600;
}

.input-group-text {
  @apply flex items-center justify-center border border-slate-200  bg-white px-3 text-base font-light text-slate-400
   transition duration-300 ease-in-out ltr:rounded-tl ltr:rounded-bl rtl:rounded-tr rtl:rounded-br dark:border-slate-700 dark:bg-slate-900;
}
.inputGroup.has-prepend {
  .input-group-control {
    @apply ltr:rounded-tl-[0] ltr:rounded-bl-[0] ltr:border-l-0 rtl:rounded-tr-[0] rtl:rounded-br-[0] rtl:border-r-0;
  }
}
.inputGroup {
  &.has-prepend-slot {
    .input-group-control {
      @apply focus:border-slate-600 focus:ring-0 ltr:rounded-tl-[0] ltr:rounded-bl-[0] ltr:border-l-0 rtl:rounded-tr-[0] rtl:rounded-br-[0] rtl:border-r-0 dark:focus:border-slate-700;
    }
  }
  &.has-append-slot {
    .input-group-control {
      @apply focus:border-slate-600 focus:ring-0 ltr:rounded-tr-[0] ltr:rounded-br-[0] ltr:border-r-0 rtl:rounded-tl-[0] rtl:rounded-bl-[0] rtl:border-l-0 dark:focus:border-slate-700;
    }
  }
}
.inputGroup.has-append {
  .input-group-control {
    @apply rounded-br-[0] ltr:rounded-tr-[0] ltr:border-r-0 rtl:rounded-tl-[0] rtl:rounded-bl-[0] rtl:border-l-0;
  }
  .input-group-addon.right {
    .input-group-text {
      @apply ltr:rounded-tl-[0] ltr:rounded-bl-[0] ltr:rounded-tr ltr:rounded-br rtl:rounded-tl  rtl:rounded-bl rtl:rounded-tr-[0] rtl:rounded-br-[0];
    }
  }
}

.inputGroup:focus-within .input-group-text {
  @apply border-black-500 dark:border-slate-900;
}
/* .merged .inputGroup:focus-within .input-group-text {
} */
.inputGroup {
  &.is-invalid {
    .input-group-text {
      @apply border-danger-500;
    }
    &:focus-within .input-group-text {
      @apply ring-danger-500;
    }
  }
  &.is-valid {
    .input-group-text {
      @apply border-success-500;
    }
    &:focus-within .input-group-text {
      @apply ring-success-500;
    }
  }
}
.prepend-slot,
.append-slot {
  .btn {
    @apply -mx-3 h-full items-center rounded-tr-[0] rounded-br-[0] pt-0 pb-0 hover:ring-0;
  }
  > div,
  button {
    @apply h-full;
  }
}
.input-group-addon {
  &.right {
    .append-slot {
      .btn {
        @apply -mx-3 rounded-tl-[0] rounded-bl-[0] rounded-tr  rounded-br;
      }
    }
  }
}
.merged {
  .input-group-addon {
    .input-group-text {
      @apply ltr:border-r-0  ltr:pr-0 rtl:border-l-0 rtl:pl-0;
    }
    &.right {
      .input-group-text {
        @apply ltr:border-l-0 ltr:border-r ltr:pr-3 ltr:pl-0 rtl:border-r-0 rtl:border-l rtl:pl-3 rtl:pr-0;
      }
    }
  }
}
