// rich editor
.dashcode-app {
  .ql-editor {
    min-height: 120px;
  }
  .ql-toolbar.ql-snow {
    @apply mb-2 border-none p-0;
  }
  .ql-container.ql-snow {
    @apply border-none bg-[#FBFBFB] text-base;
  }
  .ql-editor {
    @apply rounded border border-slate-200 text-base dark:border-slate-700;
  }
}
.dark {
  .ql-editor {
    @apply border-slate-700 bg-slate-900 text-slate-300 placeholder:text-slate-300;
  }
  .ql-editor.ql-blank::before {
    @apply text-slate-500;
  }
  .ql-snow .ql-stroke {
    @apply stroke-slate-300;
  }
  .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
    @apply bg-slate-700;
  }
  .ql-snow.ql-toolbar button:hover,
  .ql-snow .ql-toolbar button:hover,
  .ql-snow.ql-toolbar button:focus,
  .ql-snow .ql-toolbar button:focus,
  .ql-snow.ql-toolbar .ql-picker-label:hover,
  .ql-snow .ql-toolbar .ql-picker-label:hover,
  .ql-snow.ql-toolbar .ql-picker-item:hover,
  .ql-snow .ql-toolbar .ql-picker-item:hover {
    @apply bg-slate-700;
  }
  .ql-picker-label {
    @apply text-slate-300;
  }
  .ql-snow .ql-picker.ql-expanded .ql-picker-label {
    @apply border-slate-700 bg-slate-300;
  }
}
