@extends('layouts.app')

@section('content')
<div class="content-wrapper transition-all duration-150 ltr:ml-0 xl:ltr:ml-[248px]">
    <div class="page-content">
        <div class="transition-all duration-150 container-fluid" id="page_layout">
            <div id="content_layout">
                <!-- Breadcrumb -->
                <x-breadcrumb :breadcrumbItems="$breadcrumbItems" :pageTitle="$pageTitle" />

                <!-- Key Metrics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <!-- Total Users -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white">{{ number_format($metrics['totalUsers']) }}</h3>
                                    <p class="text-slate-500 dark:text-slate-400">Total Users</p>
                                </div>
                                <div class="w-12 h-12 bg-primary-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                    <iconify-icon icon="heroicons-outline:users" class="text-primary-500 text-2xl"></iconify-icon>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Active Subscriptions -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white">{{ number_format($metrics['activeSubscriptions']) }}</h3>
                                    <p class="text-slate-500 dark:text-slate-400">Active Subscriptions</p>
                                </div>
                                <div class="w-12 h-12 bg-success-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                    <iconify-icon icon="heroicons-outline:credit-card" class="text-success-500 text-2xl"></iconify-icon>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Revenue -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white">${{ number_format($metrics['monthlyRevenue'], 2) }}</h3>
                                    <p class="text-slate-500 dark:text-slate-400">Monthly Revenue</p>
                                </div>
                                <div class="w-12 h-12 bg-warning-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                    <iconify-icon icon="heroicons-outline:currency-dollar" class="text-warning-500 text-2xl"></iconify-icon>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Churn Rate -->
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white">{{ $metrics['churnRate'] }}%</h3>
                                    <p class="text-slate-500 dark:text-slate-400">Churn Rate</p>
                                </div>
                                <div class="w-12 h-12 bg-danger-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                    <iconify-icon icon="heroicons-outline:chart-bar" class="text-danger-500 text-2xl"></iconify-icon>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Monthly Revenue Chart -->
                    <div class="card">
                        <header class="card-header">
                            <h4 class="card-title">Monthly Revenue Trend</h4>
                        </header>
                        <div class="card-body">
                            <canvas id="monthlyRevenueChart" height="300"></canvas>
                        </div>
                    </div>

                    <!-- Subscription Status Distribution -->
                    <div class="card">
                        <header class="card-header">
                            <h4 class="card-title">Subscription Status Distribution</h4>
                        </header>
                        <div class="card-body">
                            <canvas id="subscriptionStatusChart" height="300"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity and Top Products -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Recent Users -->
                    <div class="card">
                        <header class="card-header">
                            <h4 class="card-title">Recent Users</h4>
                            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary btn-sm">View All</a>
                        </header>
                        <div class="card-body">
                            <div class="space-y-4">
                                @forelse($recentActivity['users'] as $user)
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-slate-600 dark:text-slate-300">
                                                {{ strtoupper(substr($user->name, 0, 2)) }}
                                            </span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-900 dark:text-white">{{ $user->name }}</p>
                                            <p class="text-sm text-slate-500 dark:text-slate-400">{{ $user->email }}</p>
                                        </div>
                                    </div>
                                    <span class="text-sm text-slate-500 dark:text-slate-400">
                                        {{ $user->created_at->diffForHumans() }}
                                    </span>
                                </div>
                                @empty
                                <p class="text-slate-500 dark:text-slate-400 text-center py-4">No recent users</p>
                                @endforelse
                            </div>
                        </div>
                    </div>

                    <!-- Top Performing Products -->
                    <div class="card">
                        <header class="card-header">
                            <h4 class="card-title">Top Performing Products</h4>
                            <a href="{{ route('admin.products.index') }}" class="btn btn-outline-primary btn-sm">Manage Products</a>
                        </header>
                        <div class="card-body">
                            <div class="space-y-4">
                                @forelse($topProducts as $product)
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-slate-900 dark:text-white">{{ $product->name }}</p>
                                        <p class="text-sm text-slate-500 dark:text-slate-400">
                                            ${{ number_format($product->price / 100, 2) }} / {{ $product->billing_cycle }}
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-medium text-slate-900 dark:text-white">{{ $product->subscriptions_count }}</p>
                                        <p class="text-sm text-slate-500 dark:text-slate-400">subscribers</p>
                                    </div>
                                </div>
                                @empty
                                <p class="text-slate-500 dark:text-slate-400 text-center py-4">No products available</p>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Payments -->
                <div class="card">
                    <header class="card-header">
                        <h4 class="card-title">Recent Payments</h4>
                        <a href="{{ route('admin.financial.payments') }}" class="btn btn-outline-primary btn-sm">View All</a>
                    </header>
                    <div class="card-body">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-slate-100 dark:divide-slate-700">
                                <thead class="bg-slate-50 dark:bg-slate-800">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">User</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">Date</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-100 dark:divide-slate-700">
                                    @forelse($recentActivity['payments'] as $payment)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-slate-900 dark:text-white">{{ $payment->user->name }}</div>
                                            <div class="text-sm text-slate-500 dark:text-slate-400">{{ $payment->user->email }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-900 dark:text-white">
                                            ${{ number_format($payment->amount / 100, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                @if($payment->status === 'succeeded') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                                @elseif($payment->status === 'failed') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                                @else bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100 @endif">
                                                {{ ucfirst($payment->status) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500 dark:text-slate-400">
                                            {{ $payment->created_at->format('M d, Y') }}
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="4" class="px-6 py-4 text-center text-slate-500 dark:text-slate-400">No recent payments</td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Monthly Revenue Chart
    const monthlyRevenueCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
    new Chart(monthlyRevenueCtx, {
        type: 'line',
        data: {
            labels: @json($chartData['monthlyRevenue']['labels']),
            datasets: [{
                label: 'Revenue ($)',
                data: @json($chartData['monthlyRevenue']['data']),
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Subscription Status Chart
    const subscriptionStatusCtx = document.getElementById('subscriptionStatusChart').getContext('2d');
    const statusData = @json($chartData['subscriptionStats']);

    new Chart(subscriptionStatusCtx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(statusData).map(status => status.charAt(0).toUpperCase() + status.slice(1)),
            datasets: [{
                data: Object.values(statusData),
                backgroundColor: [
                    'rgb(34, 197, 94)',   // active - green
                    'rgb(239, 68, 68)',   // cancelled - red
                    'rgb(245, 158, 11)',  // paused - yellow
                    'rgb(168, 85, 247)',  // past_due - purple
                    'rgb(59, 130, 246)'   // trialing - blue
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
@endpush
