@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ $pageTitle }}</h1>
            <x-breadcrumb :items="$breadcrumbItems" />
        </div>
        <div>
            @if($invoice->invoice_pdf_url)
                <a href="{{ $invoice->invoice_pdf_url }}" target="_blank" class="btn btn-info">
                    <i class="fas fa-download mr-2"></i>Download PDF
                </a>
            @endif
            <a href="{{ route('admin.financial.invoices') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>Back to Invoices
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Invoice Information -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Invoice Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="font-weight-bold">Invoice Number:</td>
                                    <td>{{ $invoice->invoice_number }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Stripe Invoice ID:</td>
                                    <td>
                                        @if($invoice->stripe_invoice_id)
                                            <code>{{ $invoice->stripe_invoice_id }}</code>
                                        @else
                                            <span class="text-muted">N/A</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Amount:</td>
                                    <td class="h5 text-success">${{ number_format($invoice->amount / 100, 2) }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Status:</td>
                                    <td>
                                        @php
                                            $statusColors = [
                                                'draft' => 'secondary',
                                                'open' => 'warning',
                                                'paid' => 'success',
                                                'void' => 'danger',
                                                'uncollectible' => 'dark'
                                            ];
                                            $statusColor = $statusColors[$invoice->status] ?? 'secondary';
                                        @endphp
                                        <span class="badge badge-{{ $statusColor }} badge-lg">
                                            {{ ucfirst($invoice->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Currency:</td>
                                    <td>{{ strtoupper($invoice->currency ?? 'USD') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="font-weight-bold">Created:</td>
                                    <td>{{ $invoice->created_at->format('M d, Y H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Due Date:</td>
                                    <td>
                                        @if($invoice->due_date)
                                            {{ $invoice->due_date->format('M d, Y') }}
                                            @if($invoice->due_date->isPast() && $invoice->status === 'open')
                                                <br><small class="text-danger font-weight-bold">Overdue</small>
                                            @endif
                                        @else
                                            <span class="text-muted">N/A</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Paid Date:</td>
                                    <td>
                                        @if($invoice->paid_at)
                                            {{ $invoice->paid_at->format('M d, Y H:i:s') }}
                                        @else
                                            <span class="text-muted">Not paid</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Updated:</td>
                                    <td>{{ $invoice->updated_at->format('M d, Y H:i:s') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Information</h6>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="icon-circle bg-primary">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        </div>
                        <div class="col">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="font-weight-bold">{{ $invoice->user->name }}</div>
                                    <div class="text-gray-500">{{ $invoice->user->email }}</div>
                                </div>
                                <div class="col-md-6">
                                    <div class="small text-gray-500">
                                        <strong>User ID:</strong> #{{ $invoice->user->id }}<br>
                                        <strong>Joined:</strong> {{ $invoice->user->created_at->format('M d, Y') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <a href="{{ route('admin.users.show', $invoice->user) }}" class="btn btn-sm btn-outline-primary">
                                View Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Information -->
            @if($invoice->subscription)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Subscription Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="font-weight-bold">Product:</td>
                                        <td>
                                            @if($invoice->subscription->product)
                                                <span class="badge badge-info">{{ $invoice->subscription->product->name }}</span>
                                            @else
                                                <span class="text-muted">N/A</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Status:</td>
                                        <td>
                                            <span class="badge badge-{{ $invoice->subscription->status === 'active' ? 'success' : 'warning' }}">
                                                {{ ucfirst($invoice->subscription->status) }}
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="font-weight-bold">Started:</td>
                                        <td>{{ $invoice->subscription->created_at->format('M d, Y') }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Next Billing:</td>
                                        <td>
                                            @if($invoice->subscription->current_period_end)
                                                {{ $invoice->subscription->current_period_end->format('M d, Y') }}
                                            @else
                                                <span class="text-muted">N/A</span>
                                            @endif
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="{{ route('admin.subscriptions.show', $invoice->subscription) }}" class="btn btn-sm btn-outline-info">
                                View Subscription Details
                            </a>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Invoice Line Items -->
            @if($invoice->line_items && count($invoice->line_items) > 0)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Line Items</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Description</th>
                                        <th>Quantity</th>
                                        <th>Unit Price</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($invoice->line_items as $item)
                                        <tr>
                                            <td>{{ $item['description'] ?? 'N/A' }}</td>
                                            <td>{{ $item['quantity'] ?? 1 }}</td>
                                            <td>${{ number_format(($item['unit_amount'] ?? 0) / 100, 2) }}</td>
                                            <td>${{ number_format(($item['amount'] ?? 0) / 100, 2) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                                <tfoot>
                                    <tr class="font-weight-bold">
                                        <td colspan="3" class="text-right">Total:</td>
                                        <td>${{ number_format($invoice->amount / 100, 2) }}</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-xl-4 col-lg-5">
            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        @if($invoice->invoice_pdf_url)
                            <a href="{{ $invoice->invoice_pdf_url }}" target="_blank" class="list-group-item list-group-item-action">
                                <i class="fas fa-download text-info mr-2"></i>
                                Download PDF
                            </a>
                        @endif
                        <a href="{{ route('admin.users.show', $invoice->user) }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-user text-primary mr-2"></i>
                            View Customer Profile
                        </a>
                        @if($invoice->subscription)
                            <a href="{{ route('admin.subscriptions.show', $invoice->subscription) }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-credit-card text-info mr-2"></i>
                                View Subscription
                            </a>
                        @endif
                        <a href="{{ route('admin.financial.invoices') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-arrow-left text-secondary mr-2"></i>
                            Back to Invoices
                        </a>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            @if($invoice->payment)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Payment Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="mr-3">
                                <div class="icon-circle bg-success">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="font-weight-bold">Payment #{{ $invoice->payment->id }}</div>
                                <div class="small text-gray-500">
                                    {{ $invoice->payment->created_at->format('M d, Y H:i') }}
                                </div>
                                <div class="small text-gray-600">
                                    ${{ number_format($invoice->payment->amount / 100, 2) }}
                                </div>
                            </div>
                            <div>
                                <a href="{{ route('admin.financial.payments.show', $invoice->payment) }}" class="btn btn-sm btn-outline-primary">
                                    View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Invoice Summary -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Summary</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <div class="h5 font-weight-bold">${{ number_format($invoice->amount / 100, 2) }}</div>
                                <div class="small text-gray-500">Total Amount</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="h5 font-weight-bold text-{{ $invoice->status === 'paid' ? 'success' : 'warning' }}">
                                {{ ucfirst($invoice->status) }}
                            </div>
                            <div class="small text-gray-500">Status</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
