@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ $pageTitle }}</h1>
            <x-breadcrumb :items="$breadcrumbItems" />
        </div>
        <div>
            @if($payment->status === 'succeeded' && $payment->amount > 0)
                <button type="button" class="btn btn-warning" data-toggle="modal" data-target="#refundModal">
                    <i class="fas fa-undo mr-2"></i>Process Refund
                </button>
            @endif
            <a href="{{ route('admin.financial.payments') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>Back to Payments
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Payment Information -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Payment Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="font-weight-bold">Payment ID:</td>
                                    <td>#{{ $payment->id }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Stripe Payment Intent:</td>
                                    <td>
                                        @if($payment->stripe_payment_intent_id)
                                            <code>{{ $payment->stripe_payment_intent_id }}</code>
                                        @else
                                            <span class="text-muted">N/A</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Amount:</td>
                                    <td class="h5 text-success">${{ number_format($payment->amount / 100, 2) }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Status:</td>
                                    <td>
                                        <span class="badge badge-{{ $payment->status === 'succeeded' ? 'success' : ($payment->status === 'failed' ? 'danger' : 'warning') }} badge-lg">
                                            {{ ucfirst($payment->status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Currency:</td>
                                    <td>{{ strtoupper($payment->currency ?? 'USD') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="font-weight-bold">Payment Method:</td>
                                    <td>
                                        @if($payment->payment_method_type)
                                            <i class="fas fa-credit-card mr-1"></i>
                                            {{ ucfirst($payment->payment_method_type) }}
                                            @if($payment->payment_method_last4)
                                                ****{{ $payment->payment_method_last4 }}
                                            @endif
                                        @else
                                            <span class="text-muted">N/A</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Created:</td>
                                    <td>{{ $payment->created_at->format('M d, Y H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <td class="font-weight-bold">Updated:</td>
                                    <td>{{ $payment->updated_at->format('M d, Y H:i:s') }}</td>
                                </tr>
                                @if($payment->failure_reason)
                                    <tr>
                                        <td class="font-weight-bold">Failure Reason:</td>
                                        <td class="text-danger">{{ $payment->failure_reason }}</td>
                                    </tr>
                                @endif
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Information</h6>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="icon-circle bg-primary">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        </div>
                        <div class="col">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="font-weight-bold">{{ $payment->user->name }}</div>
                                    <div class="text-gray-500">{{ $payment->user->email }}</div>
                                </div>
                                <div class="col-md-6">
                                    <div class="small text-gray-500">
                                        <strong>User ID:</strong> #{{ $payment->user->id }}<br>
                                        <strong>Joined:</strong> {{ $payment->user->created_at->format('M d, Y') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <a href="{{ route('admin.users.show', $payment->user) }}" class="btn btn-sm btn-outline-primary">
                                View Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Information -->
            @if($payment->subscription)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Subscription Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="font-weight-bold">Product:</td>
                                        <td>
                                            @if($payment->subscription->product)
                                                <span class="badge badge-info">{{ $payment->subscription->product->name }}</span>
                                            @else
                                                <span class="text-muted">N/A</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Status:</td>
                                        <td>
                                            <span class="badge badge-{{ $payment->subscription->status === 'active' ? 'success' : 'warning' }}">
                                                {{ ucfirst($payment->subscription->status) }}
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="font-weight-bold">Started:</td>
                                        <td>{{ $payment->subscription->created_at->format('M d, Y') }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Next Billing:</td>
                                        <td>
                                            @if($payment->subscription->current_period_end)
                                                {{ $payment->subscription->current_period_end->format('M d, Y') }}
                                            @else
                                                <span class="text-muted">N/A</span>
                                            @endif
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="{{ route('admin.subscriptions.show', $payment->subscription) }}" class="btn btn-sm btn-outline-info">
                                View Subscription Details
                            </a>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-xl-4 col-lg-5">
            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        @if($payment->status === 'succeeded' && $payment->amount > 0)
                            <button type="button" class="list-group-item list-group-item-action" 
                                    data-toggle="modal" data-target="#refundModal">
                                <i class="fas fa-undo text-warning mr-2"></i>
                                Process Refund
                            </button>
                        @endif
                        <a href="{{ route('admin.users.show', $payment->user) }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-user text-primary mr-2"></i>
                            View Customer Profile
                        </a>
                        @if($payment->subscription)
                            <a href="{{ route('admin.subscriptions.show', $payment->subscription) }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-credit-card text-info mr-2"></i>
                                View Subscription
                            </a>
                        @endif
                        <a href="{{ route('admin.financial.payments') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-arrow-left text-secondary mr-2"></i>
                            Back to Payments
                        </a>
                    </div>
                </div>
            </div>

            <!-- Refunds -->
            @if($payment->refunds && $payment->refunds->count() > 0)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Refunds</h6>
                    </div>
                    <div class="card-body">
                        @foreach($payment->refunds as $refund)
                            <div class="d-flex align-items-center border-bottom py-2">
                                <div class="mr-3">
                                    <div class="icon-circle bg-warning">
                                        <i class="fas fa-undo text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="font-weight-bold">${{ number_format($refund->amount / 100, 2) }}</div>
                                    <div class="small text-gray-500">{{ $refund->created_at->format('M d, Y') }}</div>
                                    @if($refund->reason)
                                        <div class="small text-gray-600">{{ $refund->reason }}</div>
                                    @endif
                                </div>
                                <div>
                                    <span class="badge badge-{{ $refund->status === 'succeeded' ? 'success' : ($refund->status === 'failed' ? 'danger' : 'warning') }}">
                                        {{ ucfirst($refund->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Refund Modal -->
@if($payment->status === 'succeeded' && $payment->amount > 0)
<div class="modal fade" id="refundModal" tabindex="-1" role="dialog" aria-labelledby="refundModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="refundModalLabel">Process Refund for Payment #{{ $payment->id }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('admin.financial.refunds.process') }}">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="payment_id" value="{{ $payment->id }}">

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        You are about to process a refund for <strong>{{ $payment->user->name }}</strong>.
                    </div>

                    <div class="form-group">
                        <label for="refund_amount">Refund Amount</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number" class="form-control" id="refund_amount" name="amount"
                                   step="0.01" min="0.01" max="{{ $payment->amount / 100 }}"
                                   value="{{ $payment->amount / 100 }}" required>
                        </div>
                        <small class="form-text text-muted">
                            Maximum refundable amount: ${{ number_format($payment->amount / 100, 2) }}
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="refund_reason">Reason for Refund</label>
                        <textarea class="form-control" id="refund_reason" name="reason" rows="3"
                                  placeholder="Please provide a reason for this refund..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-undo mr-2"></i>Process Refund
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@endsection
