@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ $pageTitle }}</h1>
            <x-breadcrumb :items="$breadcrumbItems" />
        </div>
    </div>

    <!-- Filters Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Search & Filter</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.financial.payments') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="q" class="form-label">Search</label>
                        <input type="text" class="form-control" id="q" name="q" 
                               value="{{ $filters['q'] }}" placeholder="Search by user name or email">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-control" id="status" name="status">
                            <option value="">All Statuses</option>
                            @foreach($statusOptions as $status)
                                <option value="{{ $status }}" {{ $filters['status'] === $status ? 'selected' : '' }}>
                                    {{ ucfirst($status) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_from" class="form-label">Date From</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" 
                               value="{{ $filters['date_from'] }}">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_to" class="form-label">Date To</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" 
                               value="{{ $filters['date_to'] }}">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="per_page" class="form-label">Per Page</label>
                        <select class="form-control" id="per_page" name="per_page">
                            <option value="10" {{ $filters['per_page'] == 10 ? 'selected' : '' }}>10</option>
                            <option value="25" {{ $filters['per_page'] == 25 ? 'selected' : '' }}>25</option>
                            <option value="50" {{ $filters['per_page'] == 50 ? 'selected' : '' }}>50</option>
                            <option value="100" {{ $filters['per_page'] == 100 ? 'selected' : '' }}>100</option>
                        </select>
                    </div>
                    <div class="col-md-1 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <a href="{{ route('admin.financial.payments') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-times mr-1"></i>Clear Filters
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                Payments ({{ $payments->total() }} total)
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Product</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Payment Method</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($payments as $payment)
                            <tr>
                                <td>
                                    <a href="{{ route('admin.financial.payments.show', $payment) }}" class="text-primary font-weight-bold">
                                        #{{ $payment->id }}
                                    </a>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <div class="icon-circle bg-primary">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="small font-weight-bold">{{ $payment->user->name }}</div>
                                            <div class="small text-gray-500">{{ $payment->user->email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($payment->subscription && $payment->subscription->product)
                                        <span class="badge badge-info">{{ $payment->subscription->product->name }}</span>
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td class="font-weight-bold">${{ number_format($payment->amount / 100, 2) }}</td>
                                <td>
                                    <span class="badge badge-{{ $payment->status === 'succeeded' ? 'success' : ($payment->status === 'failed' ? 'danger' : 'warning') }}">
                                        {{ ucfirst($payment->status) }}
                                    </span>
                                </td>
                                <td>
                                    @if($payment->payment_method_type)
                                        <i class="fas fa-credit-card mr-1"></i>
                                        {{ ucfirst($payment->payment_method_type) }}
                                        @if($payment->payment_method_last4)
                                            ****{{ $payment->payment_method_last4 }}
                                        @endif
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td>{{ $payment->created_at->format('M d, Y H:i') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.financial.payments.show', $payment) }}" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($payment->status === 'succeeded' && $payment->amount > 0)
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    data-toggle="modal" data-target="#refundModal" 
                                                    data-payment-id="{{ $payment->id }}"
                                                    data-payment-amount="{{ $payment->amount / 100 }}"
                                                    data-user-name="{{ $payment->user->name }}"
                                                    title="Process Refund">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center text-gray-500 py-4">
                                    <i class="fas fa-credit-card fa-3x mb-3 text-gray-300"></i>
                                    <div>No payments found matching your criteria</div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($payments->hasPages())
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-muted">
                        Showing {{ $payments->firstItem() }} to {{ $payments->lastItem() }} of {{ $payments->total() }} results
                    </div>
                    <div>
                        {{ $payments->appends(request()->query())->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Refund Modal -->
<div class="modal fade" id="refundModal" tabindex="-1" role="dialog" aria-labelledby="refundModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="refundModalLabel">Process Refund</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('admin.financial.refunds.process') }}">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="payment_id" id="refund_payment_id">

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        You are about to process a refund for <strong id="refund_user_name"></strong>.
                    </div>

                    <div class="form-group">
                        <label for="refund_amount">Refund Amount</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="number" class="form-control" id="refund_amount" name="amount"
                                   step="0.01" min="0.01" required>
                        </div>
                        <small class="form-text text-muted">
                            Maximum refundable amount: $<span id="max_refund_amount"></span>
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="refund_reason">Reason for Refund</label>
                        <textarea class="form-control" id="refund_reason" name="reason" rows="3"
                                  placeholder="Please provide a reason for this refund..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-undo mr-2"></i>Process Refund
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    $('#refundModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var paymentId = button.data('payment-id');
        var paymentAmount = button.data('payment-amount');
        var userName = button.data('user-name');

        var modal = $(this);
        modal.find('#refund_payment_id').val(paymentId);
        modal.find('#refund_user_name').text(userName);
        modal.find('#max_refund_amount').text(paymentAmount);
        modal.find('#refund_amount').attr('max', paymentAmount).val(paymentAmount);
    });
});
</script>
@endpush
@endsection
