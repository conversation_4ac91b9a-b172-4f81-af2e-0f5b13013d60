@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ $pageTitle }}</h1>
            <x-breadcrumb :items="$breadcrumbItems" />
        </div>
    </div>

    <!-- Filters Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Search & Filter</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.financial.refunds') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="q" class="form-label">Search</label>
                        <input type="text" class="form-control" id="q" name="q" 
                               value="{{ $filters['q'] }}" placeholder="Search by user name or email">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-control" id="status" name="status">
                            <option value="">All Statuses</option>
                            @foreach($statusOptions as $status)
                                <option value="{{ $status }}" {{ $filters['status'] === $status ? 'selected' : '' }}>
                                    {{ ucfirst($status) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_from" class="form-label">Date From</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" 
                               value="{{ $filters['date_from'] }}">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_to" class="form-label">Date To</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" 
                               value="{{ $filters['date_to'] }}">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="per_page" class="form-label">Per Page</label>
                        <select class="form-control" id="per_page" name="per_page">
                            <option value="10" {{ $filters['per_page'] == 10 ? 'selected' : '' }}>10</option>
                            <option value="25" {{ $filters['per_page'] == 25 ? 'selected' : '' }}>25</option>
                            <option value="50" {{ $filters['per_page'] == 50 ? 'selected' : '' }}>50</option>
                            <option value="100" {{ $filters['per_page'] == 100 ? 'selected' : '' }}>100</option>
                        </select>
                    </div>
                    <div class="col-md-1 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <a href="{{ route('admin.financial.refunds') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-times mr-1"></i>Clear Filters
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Refunds Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                Refunds ({{ $refunds->total() }} total)
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Payment</th>
                            <th>Customer</th>
                            <th>Product</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Reason</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($refunds as $refund)
                            <tr>
                                <td>
                                    <span class="font-weight-bold text-warning">#{{ $refund->id }}</span>
                                </td>
                                <td>
                                    <a href="{{ route('admin.financial.payments.show', $refund->payment) }}" class="text-primary">
                                        #{{ $refund->payment->id }}
                                    </a>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <div class="icon-circle bg-primary">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="small font-weight-bold">{{ $refund->payment->user->name }}</div>
                                            <div class="small text-gray-500">{{ $refund->payment->user->email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($refund->payment->subscription && $refund->payment->subscription->product)
                                        <span class="badge badge-info">{{ $refund->payment->subscription->product->name }}</span>
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td class="font-weight-bold text-warning">${{ number_format($refund->amount / 100, 2) }}</td>
                                <td>
                                    <span class="badge badge-{{ $refund->status === 'succeeded' ? 'success' : ($refund->status === 'failed' ? 'danger' : 'warning') }}">
                                        {{ ucfirst($refund->status) }}
                                    </span>
                                </td>
                                <td>
                                    @if($refund->reason)
                                        <span class="text-truncate d-inline-block" style="max-width: 150px;" title="{{ $refund->reason }}">
                                            {{ $refund->reason }}
                                        </span>
                                    @else
                                        <span class="text-muted">No reason provided</span>
                                    @endif
                                </td>
                                <td>{{ $refund->created_at->format('M d, Y H:i') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.financial.payments.show', $refund->payment) }}" 
                                           class="btn btn-sm btn-outline-primary" title="View Payment">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($refund->stripe_refund_id)
                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                    data-toggle="tooltip" title="Stripe Refund ID: {{ $refund->stripe_refund_id }}">
                                                <i class="fab fa-stripe"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center text-gray-500 py-4">
                                    <i class="fas fa-undo fa-3x mb-3 text-gray-300"></i>
                                    <div>No refunds found matching your criteria</div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($refunds->hasPages())
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-muted">
                        Showing {{ $refunds->firstItem() }} to {{ $refunds->lastItem() }} of {{ $refunds->total() }} results
                    </div>
                    <div>
                        {{ $refunds->appends(request()->query())->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Refund Statistics -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Successful Refunds
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $refunds->where('status', 'succeeded')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Refunds
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $refunds->where('status', 'pending')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Failed Refunds
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $refunds->where('status', 'failed')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Refunded
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ number_format($refunds->where('status', 'succeeded')->sum(function($refund) { return $refund->amount / 100; }), 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
@endpush
@endsection
