@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ $pageTitle }}</h1>
            <x-breadcrumb :items="$breadcrumbItems" />
        </div>
        <div>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" data-period="7">7 Days</button>
                <button type="button" class="btn btn-outline-primary" data-period="30">30 Days</button>
                <button type="button" class="btn btn-primary" data-period="90">90 Days</button>
                <button type="button" class="btn btn-outline-primary" data-period="365">1 Year</button>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-revenue">
                                ${{ number_format($analytics['totalRevenue'], 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Net Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="net-revenue">
                                ${{ number_format($analytics['netRevenue'], 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Refunds
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-refunds">
                                ${{ number_format($analytics['totalRefunds'], 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-undo fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Average Order Value
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="avg-order-value">
                                ${{ number_format($analytics['avgOrderValue'], 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Revenue Trend Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Revenue Trend</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                            aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">Chart Options:</div>
                            <a class="dropdown-item" href="#" data-chart-type="revenue">Revenue Only</a>
                            <a class="dropdown-item" href="#" data-chart-type="net">Net Revenue</a>
                            <a class="dropdown-item" href="#" data-chart-type="both">Both</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="revenueTrendChart" style="height: 320px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Revenue Breakdown -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Revenue by Product</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="productRevenueChart" style="height: 245px;"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        @foreach($analytics['productRevenue'] as $product => $revenue)
                            <span class="mr-2">
                                <i class="fas fa-circle" style="color: {{ $loop->index % 4 == 0 ? '#4e73df' : ($loop->index % 4 == 1 ? '#1cc88a' : ($loop->index % 4 == 2 ? '#36b9cc' : '#f6c23e')) }}"></i> {{ $product }}
                            </span>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Analytics -->
    <div class="row">
        <!-- Monthly Comparison -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Monthly Comparison</h6>
                </div>
                <div class="card-body">
                    <div class="chart-bar">
                        <canvas id="monthlyComparisonChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Methods -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Payment Methods</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="paymentMethodsChart" style="height: 245px;"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        @foreach($analytics['paymentMethods'] as $method => $count)
                            <span class="mr-2">
                                <i class="fas fa-circle" style="color: {{ $loop->index % 3 == 0 ? '#4e73df' : ($loop->index % 3 == 1 ? '#1cc88a' : '#36b9cc') }}"></i> {{ ucfirst($method) }} ({{ $count }})
                            </span>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Detailed Statistics</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Metric</th>
                            <th>Current Period</th>
                            <th>Previous Period</th>
                            <th>Change</th>
                            <th>Growth Rate</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="font-weight-bold">Total Revenue</td>
                            <td>${{ number_format($analytics['totalRevenue'], 2) }}</td>
                            <td>${{ number_format($analytics['previousRevenue'], 2) }}</td>
                            <td class="text-{{ $analytics['revenueChange'] >= 0 ? 'success' : 'danger' }}">
                                ${{ number_format(abs($analytics['revenueChange']), 2) }}
                                <i class="fas fa-arrow-{{ $analytics['revenueChange'] >= 0 ? 'up' : 'down' }}"></i>
                            </td>
                            <td class="text-{{ $analytics['revenueGrowth'] >= 0 ? 'success' : 'danger' }}">
                                {{ number_format($analytics['revenueGrowth'], 1) }}%
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Total Payments</td>
                            <td>{{ number_format($analytics['totalPayments']) }}</td>
                            <td>{{ number_format($analytics['previousPayments']) }}</td>
                            <td class="text-{{ $analytics['paymentsChange'] >= 0 ? 'success' : 'danger' }}">
                                {{ number_format(abs($analytics['paymentsChange'])) }}
                                <i class="fas fa-arrow-{{ $analytics['paymentsChange'] >= 0 ? 'up' : 'down' }}"></i>
                            </td>
                            <td class="text-{{ $analytics['paymentsGrowth'] >= 0 ? 'success' : 'danger' }}">
                                {{ number_format($analytics['paymentsGrowth'], 1) }}%
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Total Refunds</td>
                            <td>${{ number_format($analytics['totalRefunds'], 2) }}</td>
                            <td>${{ number_format($analytics['previousRefunds'], 2) }}</td>
                            <td class="text-{{ $analytics['refundsChange'] <= 0 ? 'success' : 'danger' }}">
                                ${{ number_format(abs($analytics['refundsChange']), 2) }}
                                <i class="fas fa-arrow-{{ $analytics['refundsChange'] >= 0 ? 'up' : 'down' }}"></i>
                            </td>
                            <td class="text-{{ $analytics['refundsGrowth'] <= 0 ? 'success' : 'danger' }}">
                                {{ number_format($analytics['refundsGrowth'], 1) }}%
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">Average Order Value</td>
                            <td>${{ number_format($analytics['avgOrderValue'], 2) }}</td>
                            <td>${{ number_format($analytics['previousAvgOrderValue'], 2) }}</td>
                            <td class="text-{{ $analytics['avgOrderValueChange'] >= 0 ? 'success' : 'danger' }}">
                                ${{ number_format(abs($analytics['avgOrderValueChange']), 2) }}
                                <i class="fas fa-arrow-{{ $analytics['avgOrderValueChange'] >= 0 ? 'up' : 'down' }}"></i>
                            </td>
                            <td class="text-{{ $analytics['avgOrderValueGrowth'] >= 0 ? 'success' : 'danger' }}">
                                {{ number_format($analytics['avgOrderValueGrowth'], 1) }}%
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Trend Chart
const revenueTrendCtx = document.getElementById('revenueTrendChart').getContext('2d');
const revenueTrendChart = new Chart(revenueTrendCtx, {
    type: 'line',
    data: {
        labels: @json($chartData['revenueTrend']['labels']),
        datasets: [{
            label: 'Revenue',
            data: @json($chartData['revenueTrend']['revenue']),
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.3
        }, {
            label: 'Net Revenue',
            data: @json($chartData['revenueTrend']['netRevenue']),
            borderColor: '#1cc88a',
            backgroundColor: 'rgba(28, 200, 138, 0.1)',
            borderWidth: 2,
            fill: false,
            tension: 0.3
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': $' + context.parsed.y.toLocaleString();
                    }
                }
            }
        }
    }
});

// Product Revenue Chart
const productRevenueCtx = document.getElementById('productRevenueChart').getContext('2d');
const productRevenueChart = new Chart(productRevenueCtx, {
    type: 'doughnut',
    data: {
        labels: @json(array_keys($analytics['productRevenue'])),
        datasets: [{
            data: @json(array_values($analytics['productRevenue'])),
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf', '#f4b619', '#e02d1b'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': $' + context.parsed.toLocaleString();
                    }
                }
            }
        }
    }
});

// Monthly Comparison Chart
const monthlyComparisonCtx = document.getElementById('monthlyComparisonChart').getContext('2d');
const monthlyComparisonChart = new Chart(monthlyComparisonCtx, {
    type: 'bar',
    data: {
        labels: @json($chartData['monthlyComparison']['labels']),
        datasets: [{
            label: 'Revenue',
            data: @json($chartData['monthlyComparison']['revenue']),
            backgroundColor: '#4e73df',
            borderColor: '#4e73df',
            borderWidth: 1
        }, {
            label: 'Refunds',
            data: @json($chartData['monthlyComparison']['refunds']),
            backgroundColor: '#e74a3b',
            borderColor: '#e74a3b',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': $' + context.parsed.y.toLocaleString();
                    }
                }
            }
        }
    }
});

// Payment Methods Chart
const paymentMethodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
const paymentMethodsChart = new Chart(paymentMethodsCtx, {
    type: 'pie',
    data: {
        labels: @json(array_keys($analytics['paymentMethods'])),
        datasets: [{
            data: @json(array_values($analytics['paymentMethods'])),
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc'],
            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Period filter functionality
document.querySelectorAll('[data-period]').forEach(button => {
    button.addEventListener('click', function() {
        const period = this.getAttribute('data-period');

        // Update active button
        document.querySelectorAll('[data-period]').forEach(btn => btn.classList.remove('btn-primary'));
        document.querySelectorAll('[data-period]').forEach(btn => btn.classList.add('btn-outline-primary'));
        this.classList.remove('btn-outline-primary');
        this.classList.add('btn-primary');

        // Reload page with new period
        const url = new URL(window.location);
        url.searchParams.set('period', period);
        window.location.href = url.toString();
    });
});

// Chart type toggle functionality
document.querySelectorAll('[data-chart-type]').forEach(link => {
    link.addEventListener('click', function(e) {
        e.preventDefault();
        const chartType = this.getAttribute('data-chart-type');

        if (chartType === 'revenue') {
            revenueTrendChart.data.datasets[1].hidden = true;
        } else if (chartType === 'net') {
            revenueTrendChart.data.datasets[0].hidden = true;
            revenueTrendChart.data.datasets[1].hidden = false;
        } else {
            revenueTrendChart.data.datasets[0].hidden = false;
            revenueTrendChart.data.datasets[1].hidden = false;
        }

        revenueTrendChart.update();
    });
});
</script>
@endpush
@endsection
