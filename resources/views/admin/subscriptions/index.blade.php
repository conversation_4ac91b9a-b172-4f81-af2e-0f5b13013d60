@extends('layouts.app')

@section('content')
<div class="content-wrapper transition-all duration-150 ltr:ml-0 xl:ltr:ml-[248px]">
    <div class="page-content">
        <div class="transition-all duration-150 container-fluid" id="page_layout">
            <div id="content_layout">
                <!-- Breadcrumb -->
                <x-breadcrumb :breadcrumbItems="$breadcrumbItems" :pageTitle="$pageTitle" />

                <!-- Filters and Search -->
                <div class="card mb-6">
                    <div class="card-body">
                        <form method="GET" action="{{ route('admin.subscriptions.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                            <!-- Search -->
                            <div>
                                <label for="q" class="form-label">Search Subscriptions</label>
                                <input type="text" id="q" name="q" value="{{ $filters['q'] }}" 
                                       placeholder="Search by user name or email..." class="form-control">
                            </div>

                            <!-- Status Filter -->
                            <div>
                                <label for="status" class="form-label">Status</label>
                                <select id="status" name="status" class="form-control">
                                    <option value="">All Statuses</option>
                                    @foreach($statusOptions as $statusOption)
                                        <option value="{{ $statusOption }}" {{ $filters['status'] === $statusOption ? 'selected' : '' }}>
                                            {{ ucfirst($statusOption) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Product Filter -->
                            <div>
                                <label for="product" class="form-label">Product</label>
                                <select id="product" name="product" class="form-control">
                                    <option value="">All Products</option>
                                    @foreach($products as $product)
                                        <option value="{{ $product->id }}" {{ $filters['product'] == $product->id ? 'selected' : '' }}>
                                            {{ $product->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Per Page -->
                            <div>
                                <label for="per_page" class="form-label">Per Page</label>
                                <select id="per_page" name="per_page" class="form-control">
                                    <option value="10" {{ $filters['per_page'] == 10 ? 'selected' : '' }}>10</option>
                                    <option value="25" {{ $filters['per_page'] == 25 ? 'selected' : '' }}>25</option>
                                    <option value="50" {{ $filters['per_page'] == 50 ? 'selected' : '' }}>50</option>
                                    <option value="100" {{ $filters['per_page'] == 100 ? 'selected' : '' }}>100</option>
                                </select>
                            </div>

                            <!-- Actions -->
                            <div class="flex items-end space-x-2">
                                <button type="submit" class="btn btn-primary">
                                    <iconify-icon icon="heroicons-outline:search" class="mr-2"></iconify-icon>
                                    Search
                                </button>
                                <a href="{{ route('admin.subscriptions.index') }}" class="btn btn-outline-secondary">
                                    <iconify-icon icon="heroicons-outline:x" class="mr-2"></iconify-icon>
                                    Clear
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white">{{ $subscriptions->total() }}</h3>
                                    <p class="text-slate-500 dark:text-slate-400">Total Subscriptions</p>
                                </div>
                                <div class="w-12 h-12 bg-primary-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                    <iconify-icon icon="heroicons-outline:credit-card" class="text-primary-500 text-2xl"></iconify-icon>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white">
                                        {{ $subscriptions->where('status', 'active')->count() }}
                                    </h3>
                                    <p class="text-slate-500 dark:text-slate-400">Active</p>
                                </div>
                                <div class="w-12 h-12 bg-success-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                    <iconify-icon icon="heroicons-outline:check-circle" class="text-success-500 text-2xl"></iconify-icon>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white">
                                        {{ $subscriptions->where('status', 'cancelled')->count() }}
                                    </h3>
                                    <p class="text-slate-500 dark:text-slate-400">Cancelled</p>
                                </div>
                                <div class="w-12 h-12 bg-danger-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                    <iconify-icon icon="heroicons-outline:x-circle" class="text-danger-500 text-2xl"></iconify-icon>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-2xl font-bold text-slate-900 dark:text-white">
                                        {{ $subscriptions->where('status', 'paused')->count() }}
                                    </h3>
                                    <p class="text-slate-500 dark:text-slate-400">Paused</p>
                                </div>
                                <div class="w-12 h-12 bg-warning-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                    <iconify-icon icon="heroicons-outline:pause" class="text-warning-500 text-2xl"></iconify-icon>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscriptions Table -->
                <div class="card">
                    <header class="card-header noborder">
                        <h4 class="card-title">Subscription Management</h4>
                        <div class="flex space-x-2">
                            <a href="{{ route('admin.subscriptions.analytics') }}" class="btn btn-outline-primary btn-sm">
                                <iconify-icon icon="heroicons-outline:chart-bar" class="mr-2"></iconify-icon>
                                Analytics
                            </a>
                            <span class="text-sm text-slate-500 dark:text-slate-400">
                                Showing {{ $subscriptions->firstItem() ?? 0 }} to {{ $subscriptions->lastItem() ?? 0 }} of {{ $subscriptions->total() }} subscriptions
                            </span>
                        </div>
                    </header>
                    <div class="card-body">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-slate-100 dark:divide-slate-700 table-fixed">
                                <thead class="bg-slate-50 dark:bg-slate-800">
                                    <tr>
                                        <th class="table-th">User</th>
                                        <th class="table-th">Product</th>
                                        <th class="table-th">Status</th>
                                        <th class="table-th">Started</th>
                                        <th class="table-th">Next Billing</th>
                                        <th class="table-th">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-100 dark:divide-slate-700">
                                    @forelse($subscriptions as $subscription)
                                    <tr class="hover:bg-slate-50 dark:hover:bg-slate-700">
                                        <td class="table-td">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-10 h-10 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center">
                                                    <span class="text-sm font-medium text-slate-600 dark:text-slate-300">
                                                        {{ strtoupper(substr($subscription->user->name, 0, 2)) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="font-medium text-slate-900 dark:text-white">{{ $subscription->user->name }}</div>
                                                    <div class="text-sm text-slate-500 dark:text-slate-400">{{ $subscription->user->email }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="table-td">
                                            <div>
                                                <div class="font-medium text-slate-900 dark:text-white">{{ $subscription->product->name }}</div>
                                                <div class="text-sm text-slate-500 dark:text-slate-400">
                                                    ${{ number_format($subscription->product->price / 100, 2) }} / {{ $subscription->product->billing_cycle }}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="table-td">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                @if($subscription->status === 'active') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                                @elseif($subscription->status === 'cancelled') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                                @elseif($subscription->status === 'paused') bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                                                @elseif($subscription->status === 'past_due') bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100
                                                @else bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 @endif">
                                                {{ ucfirst($subscription->status) }}
                                            </span>
                                        </td>
                                        <td class="table-td">
                                            <div class="text-sm text-slate-900 dark:text-white">{{ $subscription->created_at->format('M d, Y') }}</div>
                                            <div class="text-sm text-slate-500 dark:text-slate-400">{{ $subscription->created_at->diffForHumans() }}</div>
                                        </td>
                                        <td class="table-td">
                                            @if($subscription->current_period_end && $subscription->status === 'active')
                                                <div class="text-sm text-slate-900 dark:text-white">{{ $subscription->current_period_end->format('M d, Y') }}</div>
                                                <div class="text-sm text-slate-500 dark:text-slate-400">{{ $subscription->current_period_end->diffForHumans() }}</div>
                                            @else
                                                <span class="text-sm text-slate-500 dark:text-slate-400">-</span>
                                            @endif
                                        </td>
                                        <td class="table-td">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('admin.subscriptions.show', $subscription) }}" 
                                                   class="action-btn text-slate-600 dark:text-slate-300 hover:text-primary-600" 
                                                   title="View Details">
                                                    <iconify-icon icon="heroicons-outline:eye"></iconify-icon>
                                                </a>
                                                @if($subscription->status === 'active')
                                                    <button type="button" 
                                                            class="action-btn text-slate-600 dark:text-slate-300 hover:text-warning-600" 
                                                            title="Pause Subscription"
                                                            onclick="showActionModal({{ $subscription->id }}, 'pause')">
                                                        <iconify-icon icon="heroicons-outline:pause"></iconify-icon>
                                                    </button>
                                                    <button type="button" 
                                                            class="action-btn text-slate-600 dark:text-slate-300 hover:text-danger-600" 
                                                            title="Cancel Subscription"
                                                            onclick="showActionModal({{ $subscription->id }}, 'cancel')">
                                                        <iconify-icon icon="heroicons-outline:x-circle"></iconify-icon>
                                                    </button>
                                                @elseif($subscription->status === 'paused')
                                                    <button type="button" 
                                                            class="action-btn text-slate-600 dark:text-slate-300 hover:text-success-600" 
                                                            title="Resume Subscription"
                                                            onclick="showActionModal({{ $subscription->id }}, 'resume')">
                                                        <iconify-icon icon="heroicons-outline:play"></iconify-icon>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="6" class="table-td text-center py-8">
                                            <div class="text-slate-500 dark:text-slate-400">
                                                <iconify-icon icon="heroicons-outline:credit-card" class="text-4xl mb-2"></iconify-icon>
                                                <p>No subscriptions found</p>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($subscriptions->hasPages())
                        <div class="mt-6">
                            {{ $subscriptions->appends(request()->query())->links() }}
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Modal -->
<div id="actionModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="actionForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div id="reasonField" class="mb-3">
                        <label for="reason" class="form-label">Reason</label>
                        <textarea id="reason" name="reason" class="form-control" rows="3" 
                                  placeholder="Please provide a reason for this action..."></textarea>
                    </div>
                    <div id="immediateField" class="mb-3" style="display: none;">
                        <div class="form-check">
                            <input type="checkbox" id="immediate" name="immediate" value="1" class="form-check-input">
                            <label for="immediate" class="form-check-label">
                                Cancel immediately (otherwise cancels at period end)
                            </label>
                        </div>
                    </div>
                    <p id="confirmText">Are you sure you want to perform this action?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="confirmBtn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function showActionModal(subscriptionId, action) {
    const modal = document.getElementById('actionModal');
    const form = document.getElementById('actionForm');
    const title = document.getElementById('modalTitle');
    const confirmBtn = document.getElementById('confirmBtn');
    const confirmText = document.getElementById('confirmText');
    const reasonField = document.getElementById('reasonField');
    const immediateField = document.getElementById('immediateField');
    const reasonInput = document.getElementById('reason');

    // Reset form
    form.reset();
    reasonField.style.display = 'block';
    immediateField.style.display = 'none';

    if (action === 'cancel') {
        form.action = `/admin/subscriptions/${subscriptionId}/cancel`;
        title.textContent = 'Cancel Subscription';
        confirmBtn.textContent = 'Cancel Subscription';
        confirmBtn.className = 'btn btn-danger';
        confirmText.textContent = 'Are you sure you want to cancel this subscription?';
        immediateField.style.display = 'block';
        reasonInput.required = true;
        reasonInput.placeholder = 'Please provide a reason for cancelling this subscription...';
    } else if (action === 'pause') {
        form.action = `/admin/subscriptions/${subscriptionId}/pause`;
        title.textContent = 'Pause Subscription';
        confirmBtn.textContent = 'Pause Subscription';
        confirmBtn.className = 'btn btn-warning';
        confirmText.textContent = 'Are you sure you want to pause this subscription?';
        reasonInput.required = true;
        reasonInput.placeholder = 'Please provide a reason for pausing this subscription...';
    } else if (action === 'resume') {
        form.action = `/admin/subscriptions/${subscriptionId}/resume`;
        title.textContent = 'Resume Subscription';
        confirmBtn.textContent = 'Resume Subscription';
        confirmBtn.className = 'btn btn-success';
        confirmText.textContent = 'Are you sure you want to resume this subscription?';
        reasonField.style.display = 'none';
        reasonInput.required = false;
    }

    // Show modal (assuming Bootstrap modal)
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}
</script>
@endpush
