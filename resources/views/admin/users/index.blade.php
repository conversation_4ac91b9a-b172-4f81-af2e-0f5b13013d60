@extends('layouts.app')

@section('content')
<div class="content-wrapper transition-all duration-150 ltr:ml-0 xl:ltr:ml-[248px]">
    <div class="page-content">
        <div class="transition-all duration-150 container-fluid" id="page_layout">
            <div id="content_layout">
                <!-- Breadcrumb -->
                <x-breadcrumb :breadcrumbItems="$breadcrumbItems" :pageTitle="$pageTitle" />

                <!-- Filters and Search -->
                <div class="card mb-6">
                    <div class="card-body">
                        <form method="GET" action="{{ route('admin.users.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <!-- Search -->
                            <div>
                                <label for="q" class="form-label">Search Users</label>
                                <input type="text" id="q" name="q" value="{{ $filters['q'] }}" 
                                       placeholder="Search by name or email..." class="form-control">
                            </div>

                            <!-- Status Filter -->
                            <div>
                                <label for="status" class="form-label">Account Status</label>
                                <select id="status" name="status" class="form-control">
                                    <option value="">All Users</option>
                                    <option value="subscribed" {{ $filters['status'] === 'subscribed' ? 'selected' : '' }}>Subscribed</option>
                                    <option value="unsubscribed" {{ $filters['status'] === 'unsubscribed' ? 'selected' : '' }}>Unsubscribed</option>
                                    <option value="locked" {{ $filters['status'] === 'locked' ? 'selected' : '' }}>Locked</option>
                                </select>
                            </div>

                            <!-- Per Page -->
                            <div>
                                <label for="per_page" class="form-label">Per Page</label>
                                <select id="per_page" name="per_page" class="form-control">
                                    <option value="10" {{ $filters['per_page'] == 10 ? 'selected' : '' }}>10</option>
                                    <option value="25" {{ $filters['per_page'] == 25 ? 'selected' : '' }}>25</option>
                                    <option value="50" {{ $filters['per_page'] == 50 ? 'selected' : '' }}>50</option>
                                    <option value="100" {{ $filters['per_page'] == 100 ? 'selected' : '' }}>100</option>
                                </select>
                            </div>

                            <!-- Actions -->
                            <div class="flex items-end space-x-2">
                                <button type="submit" class="btn btn-primary">
                                    <iconify-icon icon="heroicons-outline:search" class="mr-2"></iconify-icon>
                                    Search
                                </button>
                                <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                                    <iconify-icon icon="heroicons-outline:x" class="mr-2"></iconify-icon>
                                    Clear
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="card">
                    <header class="card-header noborder">
                        <h4 class="card-title">User Management</h4>
                        <div class="flex space-x-2">
                            <span class="text-sm text-slate-500 dark:text-slate-400">
                                Showing {{ $users->firstItem() ?? 0 }} to {{ $users->lastItem() ?? 0 }} of {{ $users->total() }} users
                            </span>
                        </div>
                    </header>
                    <div class="card-body">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-slate-100 dark:divide-slate-700 table-fixed">
                                <thead class="bg-slate-50 dark:bg-slate-800">
                                    <tr>
                                        <th class="table-th">User</th>
                                        <th class="table-th">Subscription Status</th>
                                        <th class="table-th">Account Status</th>
                                        <th class="table-th">Roles</th>
                                        <th class="table-th">Joined</th>
                                        <th class="table-th">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-100 dark:divide-slate-700">
                                    @forelse($users as $user)
                                    <tr class="hover:bg-slate-50 dark:hover:bg-slate-700">
                                        <td class="table-td">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-10 h-10 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center">
                                                    <span class="text-sm font-medium text-slate-600 dark:text-slate-300">
                                                        {{ strtoupper(substr($user->name, 0, 2)) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="font-medium text-slate-900 dark:text-white">{{ $user->name }}</div>
                                                    <div class="text-sm text-slate-500 dark:text-slate-400">{{ $user->email }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="table-td">
                                            @if($user->activeSubscription)
                                                <div class="flex items-center space-x-2">
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                                        {{ ucfirst($user->activeSubscription->status) }}
                                                    </span>
                                                    <span class="text-sm text-slate-600 dark:text-slate-400">
                                                        {{ $user->activeSubscription->product->name }}
                                                    </span>
                                                </div>
                                            @else
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100">
                                                    No Subscription
                                                </span>
                                            @endif
                                        </td>
                                        <td class="table-td">
                                            @if($user->account_locked)
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                                                    Locked
                                                </span>
                                            @else
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                                    Active
                                                </span>
                                            @endif
                                        </td>
                                        <td class="table-td">
                                            <div class="flex flex-wrap gap-1">
                                                @forelse($user->roles as $role)
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                                        {{ $role->name }}
                                                    </span>
                                                @empty
                                                    <span class="text-sm text-slate-500 dark:text-slate-400">No roles</span>
                                                @endforelse
                                            </div>
                                        </td>
                                        <td class="table-td">
                                            <div class="text-sm text-slate-900 dark:text-white">{{ $user->created_at->format('M d, Y') }}</div>
                                            <div class="text-sm text-slate-500 dark:text-slate-400">{{ $user->created_at->diffForHumans() }}</div>
                                        </td>
                                        <td class="table-td">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('admin.users.show', $user) }}" 
                                                   class="action-btn text-slate-600 dark:text-slate-300 hover:text-primary-600" 
                                                   title="View Details">
                                                    <iconify-icon icon="heroicons-outline:eye"></iconify-icon>
                                                </a>
                                                <a href="{{ route('admin.users.edit', $user) }}" 
                                                   class="action-btn text-slate-600 dark:text-slate-300 hover:text-warning-600" 
                                                   title="Edit User">
                                                    <iconify-icon icon="heroicons-outline:pencil"></iconify-icon>
                                                </a>
                                                @if($user->account_locked)
                                                    <button type="button" 
                                                            class="action-btn text-slate-600 dark:text-slate-300 hover:text-success-600" 
                                                            title="Unlock Account"
                                                            onclick="toggleAccountStatus({{ $user->id }}, 'unlock')">
                                                        <iconify-icon icon="heroicons-outline:lock-open"></iconify-icon>
                                                    </button>
                                                @else
                                                    <button type="button" 
                                                            class="action-btn text-slate-600 dark:text-slate-300 hover:text-danger-600" 
                                                            title="Lock Account"
                                                            onclick="toggleAccountStatus({{ $user->id }}, 'lock')">
                                                        <iconify-icon icon="heroicons-outline:lock-closed"></iconify-icon>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="6" class="table-td text-center py-8">
                                            <div class="text-slate-500 dark:text-slate-400">
                                                <iconify-icon icon="heroicons-outline:users" class="text-4xl mb-2"></iconify-icon>
                                                <p>No users found</p>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($users->hasPages())
                        <div class="mt-6">
                            {{ $users->appends(request()->query())->links() }}
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Account Status Modal -->
<div id="accountStatusModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Lock Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="accountStatusForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div id="lockReasonField" class="mb-3">
                        <label for="reason" class="form-label">Reason for locking account</label>
                        <textarea id="reason" name="reason" class="form-control" rows="3" 
                                  placeholder="Please provide a reason for locking this account..."></textarea>
                    </div>
                    <p id="confirmText">Are you sure you want to perform this action?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="confirmBtn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function toggleAccountStatus(userId, action) {
    const modal = document.getElementById('accountStatusModal');
    const form = document.getElementById('accountStatusForm');
    const title = document.getElementById('modalTitle');
    const confirmBtn = document.getElementById('confirmBtn');
    const confirmText = document.getElementById('confirmText');
    const lockReasonField = document.getElementById('lockReasonField');
    const reasonInput = document.getElementById('reason');

    // Set form action
    form.action = `/admin/users/${userId}/account-status`;

    // Add hidden action field
    let actionInput = form.querySelector('input[name="action"]');
    if (!actionInput) {
        actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        form.appendChild(actionInput);
    }
    actionInput.value = action;

    if (action === 'lock') {
        title.textContent = 'Lock Account';
        confirmBtn.textContent = 'Lock Account';
        confirmBtn.className = 'btn btn-danger';
        confirmText.textContent = 'Are you sure you want to lock this user account?';
        lockReasonField.style.display = 'block';
        reasonInput.required = true;
    } else {
        title.textContent = 'Unlock Account';
        confirmBtn.textContent = 'Unlock Account';
        confirmBtn.className = 'btn btn-success';
        confirmText.textContent = 'Are you sure you want to unlock this user account?';
        lockReasonField.style.display = 'none';
        reasonInput.required = false;
        reasonInput.value = '';
    }

    // Show modal (assuming Bootstrap modal)
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}
</script>
@endpush
