@extends('layouts.app')

@section('content')
<div class="content-wrapper transition-all duration-150 ltr:ml-0 xl:ltr:ml-[248px]">
    <div class="page-content">
        <div class="transition-all duration-150 container-fluid" id="page_layout">
            <div id="content_layout">
                <!-- Breadcrumb -->
                <x-breadcrumb :breadcrumbItems="$breadcrumbItems" :pageTitle="$pageTitle" />

                <!-- User Overview -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- User Info Card -->
                    <div class="lg:col-span-1">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="w-24 h-24 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-2xl font-bold text-slate-600 dark:text-slate-300">
                                        {{ strtoupper(substr($user->name, 0, 2)) }}
                                    </span>
                                </div>
                                <h3 class="text-xl font-semibold text-slate-900 dark:text-white mb-2">{{ $user->name }}</h3>
                                <p class="text-slate-500 dark:text-slate-400 mb-4">{{ $user->email }}</p>
                                
                                <!-- Account Status -->
                                <div class="mb-4">
                                    @if($user->account_locked)
                                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                                            Account Locked
                                        </span>
                                    @else
                                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                            Account Active
                                        </span>
                                    @endif
                                </div>

                                <!-- Roles -->
                                <div class="mb-4">
                                    <h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Roles</h4>
                                    <div class="flex flex-wrap gap-2 justify-center">
                                        @forelse($user->roles as $role)
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                                {{ $role->name }}
                                            </span>
                                        @empty
                                            <span class="text-sm text-slate-500 dark:text-slate-400">No roles assigned</span>
                                        @endforelse
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="flex space-x-2 justify-center">
                                    <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary btn-sm">
                                        <iconify-icon icon="heroicons-outline:pencil" class="mr-2"></iconify-icon>
                                        Edit User
                                    </a>
                                    @if($user->account_locked)
                                        <button type="button" class="btn btn-success btn-sm" onclick="toggleAccountStatus({{ $user->id }}, 'unlock')">
                                            <iconify-icon icon="heroicons-outline:lock-open" class="mr-2"></iconify-icon>
                                            Unlock
                                        </button>
                                    @else
                                        <button type="button" class="btn btn-danger btn-sm" onclick="toggleAccountStatus({{ $user->id }}, 'lock')">
                                            <iconify-icon icon="heroicons-outline:lock-closed" class="mr-2"></iconify-icon>
                                            Lock
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- User Stats -->
                        <div class="card mt-6">
                            <div class="card-body">
                                <h4 class="card-title mb-4">User Statistics</h4>
                                <div class="space-y-4">
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">Member Since</span>
                                        <span class="font-medium text-slate-900 dark:text-white">{{ $user->created_at->format('M d, Y') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">Total Subscriptions</span>
                                        <span class="font-medium text-slate-900 dark:text-white">{{ $user->subscriptions->count() }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">Total Payments</span>
                                        <span class="font-medium text-slate-900 dark:text-white">{{ $user->payments->count() }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">Total Spent</span>
                                        <span class="font-medium text-slate-900 dark:text-white">
                                            ${{ number_format($user->payments->where('status', 'succeeded')->sum('amount') / 100, 2) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="lg:col-span-2">
                        <!-- Current Subscription -->
                        <div class="card mb-6">
                            <header class="card-header">
                                <h4 class="card-title">Current Subscription</h4>
                            </header>
                            <div class="card-body">
                                @if($user->subscriptions->where('status', 'active')->first())
                                    @php $activeSubscription = $user->subscriptions->where('status', 'active')->first() @endphp
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <h5 class="font-medium text-slate-900 dark:text-white mb-2">{{ $activeSubscription->product->name }}</h5>
                                            <p class="text-slate-600 dark:text-slate-400 mb-2">{{ $activeSubscription->product->description }}</p>
                                            <div class="flex items-center space-x-4">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                                    {{ ucfirst($activeSubscription->status) }}
                                                </span>
                                                <span class="text-sm text-slate-600 dark:text-slate-400">
                                                    ${{ number_format($activeSubscription->product->price / 100, 2) }} / {{ $activeSubscription->product->billing_cycle }}
                                                </span>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="space-y-2">
                                                <div class="flex justify-between">
                                                    <span class="text-slate-600 dark:text-slate-400">Started</span>
                                                    <span class="font-medium text-slate-900 dark:text-white">{{ $activeSubscription->created_at->format('M d, Y') }}</span>
                                                </div>
                                                @if($activeSubscription->current_period_end)
                                                <div class="flex justify-between">
                                                    <span class="text-slate-600 dark:text-slate-400">Next Billing</span>
                                                    <span class="font-medium text-slate-900 dark:text-white">{{ $activeSubscription->current_period_end->format('M d, Y') }}</span>
                                                </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <div class="text-center py-8">
                                        <iconify-icon icon="heroicons-outline:credit-card" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                                        <p class="text-slate-500 dark:text-slate-400">No active subscription</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Subscription History -->
                        <div class="card mb-6">
                            <header class="card-header">
                                <h4 class="card-title">Subscription History</h4>
                            </header>
                            <div class="card-body">
                                @if($user->subscriptions->count() > 0)
                                    <div class="overflow-x-auto">
                                        <table class="min-w-full divide-y divide-slate-100 dark:divide-slate-700">
                                            <thead class="bg-slate-50 dark:bg-slate-800">
                                                <tr>
                                                    <th class="table-th">Product</th>
                                                    <th class="table-th">Status</th>
                                                    <th class="table-th">Started</th>
                                                    <th class="table-th">Ended</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-100 dark:divide-slate-700">
                                                @foreach($user->subscriptions->sortByDesc('created_at') as $subscription)
                                                <tr>
                                                    <td class="table-td">
                                                        <div class="font-medium text-slate-900 dark:text-white">{{ $subscription->product->name }}</div>
                                                        <div class="text-sm text-slate-500 dark:text-slate-400">
                                                            ${{ number_format($subscription->product->price / 100, 2) }} / {{ $subscription->product->billing_cycle }}
                                                        </div>
                                                    </td>
                                                    <td class="table-td">
                                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                            @if($subscription->status === 'active') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                                            @elseif($subscription->status === 'cancelled') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                                            @else bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100 @endif">
                                                            {{ ucfirst($subscription->status) }}
                                                        </span>
                                                    </td>
                                                    <td class="table-td">{{ $subscription->created_at->format('M d, Y') }}</td>
                                                    <td class="table-td">
                                                        {{ $subscription->cancelled_at ? $subscription->cancelled_at->format('M d, Y') : '-' }}
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <div class="text-center py-8">
                                        <iconify-icon icon="heroicons-outline:clock" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                                        <p class="text-slate-500 dark:text-slate-400">No subscription history</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Recent Payments -->
                        <div class="card">
                            <header class="card-header">
                                <h4 class="card-title">Recent Payments</h4>
                                <a href="{{ route('admin.users.payment-history', $user) }}" class="btn btn-outline-primary btn-sm">View All</a>
                            </header>
                            <div class="card-body">
                                @if($user->payments->count() > 0)
                                    <div class="overflow-x-auto">
                                        <table class="min-w-full divide-y divide-slate-100 dark:divide-slate-700">
                                            <thead class="bg-slate-50 dark:bg-slate-800">
                                                <tr>
                                                    <th class="table-th">Amount</th>
                                                    <th class="table-th">Status</th>
                                                    <th class="table-th">Date</th>
                                                    <th class="table-th">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white dark:bg-slate-800 divide-y divide-slate-100 dark:divide-slate-700">
                                                @foreach($user->payments->take(5) as $payment)
                                                <tr>
                                                    <td class="table-td font-medium text-slate-900 dark:text-white">
                                                        ${{ number_format($payment->amount / 100, 2) }}
                                                    </td>
                                                    <td class="table-td">
                                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                            @if($payment->status === 'succeeded') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                                            @elseif($payment->status === 'failed') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                                            @else bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100 @endif">
                                                            {{ ucfirst($payment->status) }}
                                                        </span>
                                                    </td>
                                                    <td class="table-td">{{ $payment->created_at->format('M d, Y') }}</td>
                                                    <td class="table-td">
                                                        <a href="{{ route('admin.financial.payments.show', $payment) }}" 
                                                           class="action-btn text-slate-600 dark:text-slate-300 hover:text-primary-600" 
                                                           title="View Payment">
                                                            <iconify-icon icon="heroicons-outline:eye"></iconify-icon>
                                                        </a>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <div class="text-center py-8">
                                        <iconify-icon icon="heroicons-outline:currency-dollar" class="text-4xl text-slate-400 mb-2"></iconify-icon>
                                        <p class="text-slate-500 dark:text-slate-400">No payment history</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Account Status Modal (same as in index page) -->
<div id="accountStatusModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Lock Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="accountStatusForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div id="lockReasonField" class="mb-3">
                        <label for="reason" class="form-label">Reason for locking account</label>
                        <textarea id="reason" name="reason" class="form-control" rows="3" 
                                  placeholder="Please provide a reason for locking this account..."></textarea>
                    </div>
                    <p id="confirmText">Are you sure you want to perform this action?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="confirmBtn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function toggleAccountStatus(userId, action) {
    const modal = document.getElementById('accountStatusModal');
    const form = document.getElementById('accountStatusForm');
    const title = document.getElementById('modalTitle');
    const confirmBtn = document.getElementById('confirmBtn');
    const confirmText = document.getElementById('confirmText');
    const lockReasonField = document.getElementById('lockReasonField');
    const reasonInput = document.getElementById('reason');

    // Set form action
    form.action = `/admin/users/${userId}/account-status`;

    // Add hidden action field
    let actionInput = form.querySelector('input[name="action"]');
    if (!actionInput) {
        actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        form.appendChild(actionInput);
    }
    actionInput.value = action;

    if (action === 'lock') {
        title.textContent = 'Lock Account';
        confirmBtn.textContent = 'Lock Account';
        confirmBtn.className = 'btn btn-danger';
        confirmText.textContent = 'Are you sure you want to lock this user account?';
        lockReasonField.style.display = 'block';
        reasonInput.required = true;
    } else {
        title.textContent = 'Unlock Account';
        confirmBtn.textContent = 'Unlock Account';
        confirmBtn.className = 'btn btn-success';
        confirmText.textContent = 'Are you sure you want to unlock this user account?';
        lockReasonField.style.display = 'none';
        reasonInput.required = false;
        reasonInput.value = '';
    }

    // Show modal (assuming Bootstrap modal)
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}
</script>
@endpush
