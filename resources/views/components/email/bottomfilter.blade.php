<li data-status="personal"
    class="flex space-x-2 text-sm capitalize py-2 dark:text-slate-300 cursor-pointer items-center rtl:space-x-reverse email-categorie-list">
    <span class="bar-c  bg-danger-500 ring-danger-500  inline-block h-2 w-2 rounded-full ring-opacity-30
            transition-all duration-150 "></span>
    <span class="transition duration-150">{{ __('Personal') }}</span>
</li>

<li data-status="social"
    class="flex space-x-2 text-sm capitalize py-2 dark:text-slate-300 cursor-pointer items-center rtl:space-x-reverse email-categorie-list">
    <span class="bar-c
            bg-success-500 ring-success-500  inline-block h-2 w-2 rounded-full ring-opacity-30
            transition-all duration-150 "></span>
    <span class="transition duration-150">{{ __('Social') }}</span>
</li>

<li data-status="promotions"
    class="flex space-x-2 text-sm capitalize py-2 dark:text-slate-300 cursor-pointer items-center rtl:space-x-reverse email-categorie-list">
    <span class="bar-c  bg-warning-500 ring-warning-500  inline-block h-2 w-2 rounded-full ring-opacity-30
            transition-all duration-150 "></span>
    <span class="transition duration-150">{{ __('Promotions') }}</span>
</li>

<li data-status="business"
    class="flex space-x-2 text-sm capitalize py-2 dark:text-slate-300 cursor-pointer items-center rtl:space-x-reverse email-categorie-list">
    <span class="bar-c  bg-primary-500 ring-primary-500  inline-block h-2 w-2 rounded-full ring-opacity-30
            transition-all duration-150 "></span>
    <span class="transition duration-150">{{ __('Business') }}</span>
</li>

<!-- END: Bottom Filter -->
