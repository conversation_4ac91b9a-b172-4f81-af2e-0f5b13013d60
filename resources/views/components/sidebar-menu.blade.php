<!-- BEGIN: Sidebar -->
<div class="sidebar-wrapper group w-0 hidden xl:w-[248px] xl:block">
    <div id="bodyOverlay" class="w-screen h-screen fixed top-0 bg-slate-900 bg-opacity-50 backdrop-blur-sm z-10 hidden">
    </div>
    <div class="logo-segment">

        <!-- Application Logo -->
        <x-application-logo />

        <!-- Sidebar Type Button -->
        <div id="sidebar_type" class="cursor-pointer text-slate-900 dark:text-white text-lg">
            <iconify-icon class="sidebarDotIcon extend-icon text-slate-900 dark:text-slate-200" icon="fa-regular:dot-circle"></iconify-icon>
            <iconify-icon class="sidebarDotIcon collapsed-icon text-slate-900 dark:text-slate-200" icon="material-symbols:circle-outline"></iconify-icon>
        </div>
        <button class="sidebarCloseIcon text-2xl inline-block md:hidden">
            <iconify-icon class="text-slate-900 dark:text-slate-200" icon="clarity:window-close-line"></iconify-icon>
        </button>
    </div>
    <div id="nav_shadow" class="nav_shadow h-[60px] absolute top-[80px] nav-shadow z-[1] w-full transition-all duration-200 pointer-events-none
      opacity-0"></div>
    <div class="sidebar-menus bg-white dark:bg-slate-800 py-2 px-4 h-[calc(100%-80px)] z-50" id="sidebar_menus">
        <ul class="sidebar-menu">
            <li class="sidebar-menu-title">{{ __('MENU') }}</li>

            <!-- Dashboard -->
            <li class="{{ (request()->is('admin/dashboard*')) ? 'active' : '' }}">
                <a href="{{ route('admin.dashboard') }}" class="navItem">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="heroicons-outline:home"></iconify-icon>
                        <span>{{ __('Dashboard') }}</span>
                    </span>
                </a>
            </li>

            <!-- Admin Management -->
            @can('admin dashboard access')
            <li class="sidebar-menu-title">{{ __('MANAGEMENT') }}</li>

            <!-- Users -->
            <li>
                <a href="{{ route('admin.users.index') }}" class="navItem {{ (request()->is('admin/users*')) ? 'active' : '' }}">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="heroicons-outline:users"></iconify-icon>
                        <span>{{ __('Users') }}</span>
                    </span>
                </a>
            </li>

            <!-- Subscriptions -->
            <li>
                <a href="{{ route('admin.subscriptions.index') }}" class="navItem {{ (request()->is('admin/subscriptions*')) ? 'active' : '' }}">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="heroicons-outline:credit-card"></iconify-icon>
                        <span>{{ __('Subscriptions') }}</span>
                    </span>
                </a>
            </li>

            <!-- Products -->
            <li>
                <a href="{{ route('admin.products.index') }}" class="navItem {{ (request()->is('admin/products*')) ? 'active' : '' }}">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="heroicons-outline:cube"></iconify-icon>
                        <span>{{ __('Products') }}</span>
                    </span>
                </a>
            </li>

            <!-- Financial -->
            <li class="{{ (request()->is('admin/financial*')) ? 'active' : '' }}">
                <a href="javascript:void(0)" class="navItem">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="heroicons-outline:currency-dollar"></iconify-icon>
                        <span>{{ __('Financial') }}</span>
                    </span>
                    <iconify-icon class="icon-arrow" icon="heroicons-outline:chevron-right"></iconify-icon>
                </a>
                <ul class="sidebar-submenu">
                    <li>
                        <a href="{{ route('admin.financial.index') }}" class="navItem {{ (request()->is('admin/financial') || request()->is('admin/financial/overview*')) ? 'active' : '' }}">{{ __('Revenue Analytics') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('admin.financial.invoices') }}" class="navItem {{ (request()->is('admin/financial/invoices*')) ? 'active' : '' }}">{{ __('Invoices') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('admin.financial.payments') }}" class="navItem {{ (request()->is('admin/financial/payments*')) ? 'active' : '' }}">{{ __('Payments') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('admin.financial.refunds') }}" class="navItem {{ (request()->is('admin/financial/refunds*')) ? 'active' : '' }}">{{ __('Refunds') }}</a>
                    </li>
                </ul>
            </li>

            <!-- Reports -->
            <li>
                <a href="{{ route('admin.reports.index') }}" class="navItem {{ (request()->is('admin/reports*')) ? 'active' : '' }}">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="heroicons-outline:document-report"></iconify-icon>
                        <span>{{ __('Reports') }}</span>
                    </span>
                </a>
            </li>
            @endcan

            <!-- Settings -->
            <li class="sidebar-menu-title">{{ __('SYSTEM') }}</li>
            <li>
                <a href="{{ route('general-settings.show') }}" class="navItem {{ (request()->is('general-settings*')) || (request()->is('users*')) || (request()->is('roles*')) || (request()->is('profiles*')) || (request()->is('permissions*')) ? 'active' : '' }}">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="material-symbols:settings-outline"></iconify-icon>
                        <span>{{ __('Settings') }}</span>
                    </span>
                </a>
            </li>
        </ul>

        <!-- Upgrade Your Business Plan Card Start -->
        <div class="bg-slate-900 mb-10 mt-24 p-4 relative text-center rounded-2xl text-white" id="sidebar_bottom_wizard">
            <img src="/images/svg/rabit.svg" alt="" class="mx-auto relative -mt-[73px]">
            <div class="max-w-[160px] mx-auto mt-6">
                <div class="widget-title font-Inter mb-1">Subscription System</div>
                <div class="text-xs font-light font-Inter">
                    Manage your subscription business
                </div>
            </div>
            <div class="mt-6">
                <button class="bg-white hover:bg-opacity-80 text-slate-900 text-sm font-Inter rounded-md w-full block py-2 font-medium">
                    View Analytics
                </button>
            </div>
        </div>
        <!-- Upgrade Your Business Plan Card End -->
    </div>
</div>
<!-- End: Sidebar -->
