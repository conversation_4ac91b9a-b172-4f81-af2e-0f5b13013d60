<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\GeneralSettingsController;
use App\Http\Controllers\Api\GeneralSettingsMediaController;
use App\Http\Controllers\Api\PermissionController;
use App\Http\Controllers\Api\ProfileController;
use App\Http\Controllers\Api\RoleController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\EnvironmentController;
use App\Http\Controllers\Api\DatabaseBackupController;
use App\Http\Controllers\Api\SubscriptionProductController;
use App\Http\Controllers\Api\SubscriptionController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\PaymentMethodController;
use App\Http\Controllers\Api\InvoiceController;
use App\Http\Controllers\Api\PaymentHistoryController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\AccountStatusController;
use App\Http\Controllers\Api\RefundController;
use App\Http\Controllers\Api\StripeWebhookController;
use App\Http\Controllers\Api\Admin\AdminUserController;
use App\Http\Controllers\Api\Admin\AdminSubscriptionController;
use App\Http\Controllers\Api\Admin\AdminFinancialController;
use App\Http\Controllers\Api\Admin\AdminRefundController;
use Illuminate\Support\Facades\Route;

/*
 * API Routes
 */
Route::post('register', [AuthController::class, 'register']);
Route::post('login', [AuthController::class, 'login']);
// OAuth
Route::post('login-oauth', [AuthController::class, 'social']);

Route::post('forgot-password', [AuthController::class, 'forgotPassword']);

// Verify new email after change
Route::get('profile-verify-new-email/{token}',
    [ProfileController::class, 'verifyNewEmail'])->name('profile.verify-new-email');

// authenticated routes
Route::group(['middleware' => ['auth:sanctum', 'check.account.status']], function () {
    Route::post('resend-verification', [AuthController::class, 'resendVerification'])
        ->middleware('throttle:6,1');
    Route::get('user', [AuthController::class, 'user']);
    Route::post('logout', [AuthController::class, 'logout']);

    Route::apiSingleton('env', EnvironmentController::class);
    Route::group(['middleware' => 'verified', 'as' => 'api.v1.'], function () {
        Route::post('password-change', [AuthController::class, 'changePassword']);
        Route::apiResource('users', UserController::class);
        Route::delete('users-delete-many', [UserController::class, 'destroyMany']);
        Route::apiResource('permissions', PermissionController::class);
        Route::resource('roles', RoleController::class)->except('edit');
        Route::apiSingleton('profile', ProfileController::class);
        Route::put('general-settings-images', GeneralSettingsMediaController::class);

        // Subscription Management Routes
        Route::apiResource('subscription-products', SubscriptionProductController::class);
        Route::apiResource('subscriptions', SubscriptionController::class);

        // Payment Management Routes
        Route::apiResource('payments', PaymentController::class)->only(['index', 'show']);
        Route::post('payments/create-intent', [PaymentController::class, 'createPaymentIntent']);
        Route::post('payments/confirm', [PaymentController::class, 'confirmPayment']);
        Route::post('payments/{payment}/retry', [PaymentController::class, 'retryPayment']);

        // Payment Method Management Routes
        Route::get('payment-methods/stripe-config', [PaymentMethodController::class, 'getStripeConfig']);
        Route::apiResource('payment-methods', PaymentMethodController::class);
        Route::post('payment-methods/setup-intent', [PaymentMethodController::class, 'createSetupIntent']);
        Route::post('payment-methods/{payment_method}/set-default', [PaymentMethodController::class, 'setDefault']);

        // Invoice Management Routes
        Route::apiResource('invoices', InvoiceController::class)->only(['index', 'show']);
        Route::post('invoices/generate', [InvoiceController::class, 'generate']);
        Route::get('invoices/{invoice}/download', [InvoiceController::class, 'download']);
        Route::post('invoices/{invoice}/pay', [InvoiceController::class, 'pay']);

        // Payment History Routes
        Route::get('payment-history', [PaymentHistoryController::class, 'index']);
        Route::get('payment-history/statistics', [PaymentHistoryController::class, 'statistics']);
        Route::get('payment-history/recent', [PaymentHistoryController::class, 'recent']);
        Route::get('payment-history/export', [PaymentHistoryController::class, 'export']);

        // Notification Routes
        Route::get('notifications', [NotificationController::class, 'index']);
        Route::get('notifications/unread-count', [NotificationController::class, 'unreadCount']);
        Route::get('notifications/statistics', [NotificationController::class, 'statistics']);
        Route::post('notifications/{notification}/mark-as-read', [NotificationController::class, 'markAsRead']);
        Route::post('notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead']);
        Route::delete('notifications/{notification}', [NotificationController::class, 'destroy']);

        // Account Status Routes
        Route::get('account/status', [AccountStatusController::class, 'show']);

        // Refund Management Routes
        Route::prefix('refunds')->group(function () {
            Route::get('/', [RefundController::class, 'index']);
            Route::get('/statistics', [RefundController::class, 'statistics']);
            Route::get('/{refund}', [RefundController::class, 'show']);
            Route::post('/request', [RefundController::class, 'requestRefund']);
            Route::post('/calculate-prorated', [RefundController::class, 'calculateProratedRefund']);
        });

        // Admin Routes
        Route::prefix('admin')->group(function () {
            // User Management
            Route::apiResource('users', AdminUserController::class)->only(['index', 'show']);
            Route::put('users/{user}/account-status', [AdminUserController::class, 'updateAccountStatus']);
            Route::get('users/{user}/subscription-analytics', [AdminUserController::class, 'subscriptionAnalytics']);
            Route::get('users/{user}/payment-history', [AdminUserController::class, 'paymentHistory']);

            // Subscription Management
            Route::apiResource('subscriptions', AdminSubscriptionController::class)->only(['index', 'show']);
            Route::get('subscriptions-analytics', [AdminSubscriptionController::class, 'analytics']);
            Route::post('subscriptions/{subscription}/cancel', [AdminSubscriptionController::class, 'cancel']);
            Route::post('subscriptions/{subscription}/pause', [AdminSubscriptionController::class, 'pause']);

            // Financial Management
            Route::get('financial/overview', [AdminFinancialController::class, 'overview']);
            Route::get('financial/payments', [AdminFinancialController::class, 'payments']);
            Route::get('financial/invoices', [AdminFinancialController::class, 'invoices']);
            Route::get('financial/refunds', [AdminFinancialController::class, 'refunds']);
            Route::post('financial/process-refund', [AdminFinancialController::class, 'processRefund']);

            // Refund Management Routes
            Route::prefix('refunds')->group(function () {
                Route::get('/', [AdminRefundController::class, 'index']);
                Route::get('/statistics', [AdminRefundController::class, 'statistics']);
                Route::get('/{refund}', [AdminRefundController::class, 'show']);
                Route::post('/process', [AdminRefundController::class, 'processRefund']);
                Route::post('/process-prorated', [AdminRefundController::class, 'processProratedRefund']);
            });

            // Account Status Management Routes
            Route::post('users/{user}/lock', [AccountStatusController::class, 'lock']);
            Route::post('users/{user}/unlock', [AccountStatusController::class, 'unlock']);
            Route::post('users/{user}/suspend', [AccountStatusController::class, 'suspend']);
            Route::post('users/{user}/activate', [AccountStatusController::class, 'activate']);
            Route::post('users/bulk-status-update', [AccountStatusController::class, 'bulkUpdate']);
        });

        // Database Backup
        Route::apiResource('database-backups', DatabaseBackupController::class)->only(['index', 'destroy']);
        Route::get('database-backups-create', [DatabaseBackupController::class,'createBackup']);
        Route::get('database-backups-download/{fileName}', [DatabaseBackupController::class, 'databaseBackupDownload']);
    });
});

// General Settings
Route::get('general-settings', GeneralSettingsController::class);

// Stripe Webhook (no authentication required)
Route::post('stripe/webhook', [StripeWebhookController::class, 'handleWebhook']);
