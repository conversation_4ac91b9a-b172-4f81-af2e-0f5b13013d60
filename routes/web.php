<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SetLocaleController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\GeneralSettingController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\AdminUserManagementController;
use App\Http\Controllers\Admin\AdminSubscriptionManagementController;
use App\Http\Controllers\Admin\AdminFinancialController;
use App\Http\Controllers\Admin\AdminProductController;

require __DIR__ . '/auth.php';

Route::get('/', function () {
    return to_route('login');
});

Route::group(['middleware' => ['auth', 'verified']], function () {
    // Dashboards
    Route::get('dashboard', [HomeController::class, 'index'])->name('dashboard.index');
    // Locale
    Route::get('setlocale/{locale}', SetLocaleController::class)->name('setlocale');

    // User
    Route::resource('users', UserController::class);
    // Permission
    Route::resource('permissions', PermissionController::class)->except(['show']);
    // Roles
    Route::resource('roles', RoleController::class);
    // Profiles
    Route::resource('profiles', ProfileController::class)->only(['index', 'update'])->parameter('profiles', 'user');
    // Env
    Route::singleton('general-settings', GeneralSettingController::class);
    Route::post('general-settings-logo', [GeneralSettingController::class, 'logoUpdate'])->name('general-settings.logo');

    // Admin Dashboard Routes
    Route::prefix('admin')->name('admin.')->group(function () {
        // Main Dashboard
        Route::get('dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

        // User Management
        Route::get('users', [AdminUserManagementController::class, 'index'])->name('users.index');
        Route::get('users/{user}', [AdminUserManagementController::class, 'show'])->name('users.show');
        Route::get('users/{user}/edit', [AdminUserManagementController::class, 'edit'])->name('users.edit');
        Route::put('users/{user}', [AdminUserManagementController::class, 'update'])->name('users.update');
        Route::put('users/{user}/account-status', [AdminUserManagementController::class, 'updateAccountStatus'])->name('users.account-status');
        Route::get('users/{user}/subscription-analytics', [AdminUserManagementController::class, 'subscriptionAnalytics'])->name('users.subscription-analytics');
        Route::get('users/{user}/payment-history', [AdminUserManagementController::class, 'paymentHistory'])->name('users.payment-history');

        // Subscription Management
        Route::get('subscriptions', [AdminSubscriptionManagementController::class, 'index'])->name('subscriptions.index');
        Route::get('subscriptions/{subscription}', [AdminSubscriptionManagementController::class, 'show'])->name('subscriptions.show');
        Route::put('subscriptions/{subscription}/cancel', [AdminSubscriptionManagementController::class, 'cancel'])->name('subscriptions.cancel');
        Route::put('subscriptions/{subscription}/pause', [AdminSubscriptionManagementController::class, 'pause'])->name('subscriptions.pause');
        Route::put('subscriptions/{subscription}/resume', [AdminSubscriptionManagementController::class, 'resume'])->name('subscriptions.resume');
        Route::get('subscription-analytics', [AdminSubscriptionManagementController::class, 'analytics'])->name('subscriptions.analytics');

        // Financial Management
        Route::get('financial', [AdminFinancialController::class, 'index'])->name('financial.index');
        Route::get('payments', [AdminFinancialController::class, 'payments'])->name('financial.payments');
        Route::get('payments/{payment}', [AdminFinancialController::class, 'showPayment'])->name('financial.payments.show');
        Route::get('invoices', [AdminFinancialController::class, 'invoices'])->name('financial.invoices');
        Route::get('invoices/{invoice}', [AdminFinancialController::class, 'showInvoice'])->name('financial.invoices.show');
        Route::get('refunds', [AdminFinancialController::class, 'refunds'])->name('financial.refunds');
        Route::post('refunds', [AdminFinancialController::class, 'processRefund'])->name('financial.refunds.process');
        Route::get('revenue-analytics', [AdminFinancialController::class, 'revenueAnalytics'])->name('financial.revenue-analytics');

        // Subscription Products Management
        Route::resource('products', AdminProductController::class)->except(['show']);
        Route::put('products/{product}/toggle-status', [AdminProductController::class, 'toggleStatus'])->name('products.toggle-status');
    });
});
