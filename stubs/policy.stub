<?php

namespace {{ namespace }};

use Illuminate\Auth\Access\HandlesAuthorization;
use {{ namespacedModel }};
use {{ namespacedUserModel }};

class {{ class }}
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \{{ namespacedUserModel }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny({{ user }} $user)
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \{{ namespacedUserModel }}  $user
     * @param  \{{ namespacedModel }}  ${{ modelVariable }}
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view({{ user }} $user, {{ model }} ${{ modelVariable }})
    {
        //
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \{{ namespacedUserModel }}  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create({{ user }} $user)
    {
        //
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \{{ namespacedUserModel }}  $user
     * @param  \{{ namespacedModel }}  ${{ modelVariable }}
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update({{ user }} $user, {{ model }} ${{ modelVariable }})
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \{{ namespacedUserModel }}  $user
     * @param  \{{ namespacedModel }}  ${{ modelVariable }}
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete({{ user }} $user, {{ model }} ${{ modelVariable }})
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \{{ namespacedUserModel }}  $user
     * @param  \{{ namespacedModel }}  ${{ modelVariable }}
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore({{ user }} $user, {{ model }} ${{ modelVariable }})
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \{{ namespacedUserModel }}  $user
     * @param  \{{ namespacedModel }}  ${{ modelVariable }}
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete({{ user }} $user, {{ model }} ${{ modelVariable }})
    {
        //
    }
}
