<?php

require_once __DIR__.'/vendor/autoload.php';

try {
    $app = require_once __DIR__.'/bootstrap/app.php';
    
    // Try to boot the application like artisan does
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    
    echo "Artisan kernel created successfully!\n";
    
    // Try to handle a simple command
    $status = $kernel->handle(
        $input = new Symfony\Component\Console\Input\ArrayInput(['--version']),
        new Symfony\Component\Console\Output\ConsoleOutput
    );
    
    echo "Command handled with status: $status\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}
