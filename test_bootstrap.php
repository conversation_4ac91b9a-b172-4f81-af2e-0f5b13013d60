<?php

require_once __DIR__.'/vendor/autoload.php';

try {
    echo "Loading Laravel application...\n";
    $app = require_once __DIR__.'/bootstrap/app.php';
    echo "Laravel application loaded successfully!\n";

    echo "Manually registering core services...\n";

    // Register Filesystem service
    $app->singleton('files', function ($app) {
        return new \Illuminate\Filesystem\Filesystem;
    });

    // Register Events service
    $app->singleton('events', function ($app) {
        return new \Illuminate\Events\Dispatcher($app);
    });

    // Set facade application instance
    \Illuminate\Support\Facades\Facade::setFacadeApplication($app);

    echo "Core services registered!\n";

    echo "Booting application...\n";
    $app->boot();
    echo "Application booted successfully!\n";

    echo "Testing container...\n";
    $container = $app->make('Illuminate\Contracts\Container\Container');
    echo "Container loaded successfully!\n";

    echo "Testing cache...\n";
    $cache = $app->make('cache');
    echo "Cache loaded successfully!\n";

    echo "Testing session...\n";
    $session = $app->make('session');
    echo "Session loaded successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
