# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@esbuild/win32-x64@0.17.12":
  "integrity" "sha512-JOOxw49BVZx2/5tW3FqkdjSD/5gXYeVGPDcB0lvap0gLQshkh1Nyel1QazC+wNxus3xPlsYAgqU1BUmrmCvWtw=="
  "resolved" "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.17.12.tgz"
  "version" "0.17.12"

"@fullcalendar/core@^6.1.5", "@fullcalendar/core@~6.1.5":
  "integrity" "sha512-U3ZJy1JGAgvyXG4iLWvTW4osbkt5Fj9gmbU1B+RyRhiqwXGE0F329sX7kqLTzj3QI1IPzyq6xnJfWUIsAe7+qw=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/core/-/core-6.1.5.tgz"
  "version" "6.1.5"
  dependencies:
    "preact" "~10.12.1"

"@fullcalendar/daygrid@^6.1.5", "@fullcalendar/daygrid@~6.1.5":
  "integrity" "sha512-wXQUNPOyEQ1FEHVdtxP/tLcAAGwY4zeFejujhcznhKC751X8voInF+7kFmI7GDTVgX9cd72Muku3c+gHi8ygEQ=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/daygrid/-/daygrid-6.1.5.tgz"
  "version" "6.1.5"

"@fullcalendar/list@^6.1.5":
  "integrity" "sha512-Z34h32ZuI0+sU6GdV2WrXnF6Z17VvsagjSBY+8/bMrMHfDoLbT76sGfoI6N0GCAr7aW9N2NgWWz8kuQw9uFAZQ=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/list/-/list-6.1.5.tgz"
  "version" "6.1.5"

"@fullcalendar/timegrid@^6.1.5":
  "integrity" "sha512-phofe7XQ+JbejQB9zGqtZOGmPr/vMkfkK5dwPY4rOjxZOG/wspL3aXoSurmXaVP5CIiRWBvX9QPq0UgVJt0j5A=="
  "resolved" "https://registry.npmjs.org/@fullcalendar/timegrid/-/timegrid-6.1.5.tgz"
  "version" "6.1.5"
  dependencies:
    "@fullcalendar/daygrid" "~6.1.5"

"@iconify/types@^2.0.0":
  "integrity" "sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg=="
  "resolved" "https://registry.npmjs.org/@iconify/types/-/types-2.0.0.tgz"
  "version" "2.0.0"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  "integrity" "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  "integrity" "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg=="
  "resolved" "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@popperjs/core@^2.6.0", "@popperjs/core@^2.9.0":
  "integrity" "sha512-Cr4OjIkipTtcXKjAsm8agyleBuDHvxzeBoa1v543lbv1YaIwQjESsVcmjiWiPEbC1FIeHOG/Op9kdCmAmiS3Kw=="
  "resolved" "https://registry.npmjs.org/@popperjs/core/-/core-2.11.7.tgz"
  "version" "2.11.7"

"@swc/helpers@^0.2.13":
  "integrity" "sha512-wpCQMhf5p5GhNg2MmGKXzUNwxe7zRiCsmqYsamez2beP7mKPCSiu+BjZcdN95yYSzO857kr0VfQewmGpS77nqA=="
  "resolved" "https://registry.npmjs.org/@swc/helpers/-/helpers-0.2.14.tgz"
  "version" "0.2.14"

"@tailwindcss/aspect-ratio@^0.4.2":
  "integrity" "sha512-8QPrypskfBa7QIMuKHg2TA7BqES6vhBrDLOv8Unb6FcFyd3TjKbc6lcmb9UPQHxfl24sXoJ41ux/H7qQQvfaSQ=="
  "resolved" "https://registry.npmjs.org/@tailwindcss/aspect-ratio/-/aspect-ratio-0.4.2.tgz"
  "version" "0.4.2"

"@tailwindcss/forms@^0.5.2":
  "integrity" "sha512-y5mb86JUoiUgBjY/o6FJSFZSEttfb3Q5gllE4xoKjAAD+vBrnIhE4dViwUuow3va8mpH4s9jyUbUbrRGoRdc2Q=="
  "resolved" "https://registry.npmjs.org/@tailwindcss/forms/-/forms-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "mini-svg-data-uri" "^1.2.3"

"@tailwindcss/typography@^0.5.9":
  "integrity" "sha512-t8Sg3DyynFysV9f4JDOVISGsjazNb48AeIYQwcL+Bsq5uf4RYL75C1giZ43KISjeDGBaTN3Kxh7Xj/vRSMJUUg=="
  "resolved" "https://registry.npmjs.org/@tailwindcss/typography/-/typography-0.5.9.tgz"
  "version" "0.5.9"
  dependencies:
    "lodash.castarray" "^4.4.0"
    "lodash.isplainobject" "^4.0.6"
    "lodash.merge" "^4.6.2"
    "postcss-selector-parser" "6.0.10"

"@vue/reactivity@~3.1.1":
  "integrity" "sha512-1tdfLmNjWG6t/CsPldh+foumYFo3cpyCHgBYQ34ylaMsJ+SNHQ1kApMIa8jN+i593zQuaw3AdWH0nJTARzCFhg=="
  "resolved" "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.1.5.tgz"
  "version" "3.1.5"
  dependencies:
    "@vue/shared" "3.1.5"

"@vue/shared@3.1.5":
  "integrity" "sha512-oJ4F3TnvpXaQwZJNF3ZK+kLPHKarDmJjJ6jyzVNDKH9md1dptjC7lWR//jrGuLdek/U6iltWxqAnYOu8gCiOvA=="
  "resolved" "https://registry.npmjs.org/@vue/shared/-/shared-3.1.5.tgz"
  "version" "3.1.5"

"acorn-node@^1.8.2":
  "integrity" "sha512-8mt+fslDufLYntIoPAaIMUe/lrbrehIiwmR3t2k9LljIzoigEPF27eLk2hy8zSGzmR/ogr7zbRKINMo1u0yh5A=="
  "resolved" "https://registry.npmjs.org/acorn-node/-/acorn-node-1.8.2.tgz"
  "version" "1.8.2"
  dependencies:
    "acorn" "^7.0.0"
    "acorn-walk" "^7.0.0"
    "xtend" "^4.0.2"

"acorn-walk@^7.0.0":
  "integrity" "sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA=="
  "resolved" "https://registry.npmjs.org/acorn-walk/-/acorn-walk-7.2.0.tgz"
  "version" "7.2.0"

"acorn@^7.0.0":
  "integrity" "sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz"
  "version" "7.4.1"

"alpinejs@^3.4.2":
  "integrity" "sha512-YENcRBA9dlwR8PsZNFMTHbmdlTNwd1BkCeivPvOzzCKHas6AfwNRsDK9UEFmE5dXTMEZjnnpCTxV8vkdpWiOCw=="
  "resolved" "https://registry.npmjs.org/alpinejs/-/alpinejs-3.12.0.tgz"
  "version" "3.12.0"
  dependencies:
    "@vue/reactivity" "~3.1.1"

"animate.css@^4.1.1":
  "integrity" "sha512-+mRmCTv6SbCmtYJCN4faJMNFVNN5EuCTTprDTAo7YzIGji2KADmakjVA3+8mVDkZ2Bf09vayB35lSQIex2+QaQ=="
  "resolved" "https://registry.npmjs.org/animate.css/-/animate.css-4.1.1.tgz"
  "version" "4.1.1"

"anymatch@~3.1.2":
  "integrity" "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"apexcharts@^3.36.3":
  "integrity" "sha512-BE5+WgIjnJgLlNPiIVqH47mzhSeSHfzg5jMUN1PVZ3fxM6ZL8WEB6aaNAh0l3c9K6PitimWo7xho48Zp7mBG2w=="
  "resolved" "https://registry.npmjs.org/apexcharts/-/apexcharts-3.37.2.tgz"
  "version" "3.37.2"
  dependencies:
    "svg.draggable.js" "^2.2.2"
    "svg.easing.js" "^2.0.0"
    "svg.filter.js" "^2.0.2"
    "svg.pathmorphing.js" "^0.1.3"
    "svg.resize.js" "^1.4.3"
    "svg.select.js" "^3.0.1"

"arg@^5.0.1", "arg@^5.0.2":
  "integrity" "sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg=="
  "resolved" "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz"
  "version" "5.0.2"

"asynckit@^0.4.0":
  "integrity" "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atoa@1.0.0":
  "integrity" "sha512-VVE1H6cc4ai+ZXo/CRWoJiHXrA1qfA31DPnx6D20+kSI547hQN5Greh51LQ1baMRMfxO5K5M4ImMtZbZt2DODQ=="
  "resolved" "https://registry.npmjs.org/atoa/-/atoa-1.0.0.tgz"
  "version" "1.0.0"

"autoprefixer@^10.4.14":
  "integrity" "sha512-FQzyfOsTlwVzjHxKEqRIAdJx9niO6VCBCoEwax/VLSoQF29ggECcPuBqUMZ+u8jCZOPSy8b8/8KnuFbp0SaFZQ=="
  "resolved" "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.14.tgz"
  "version" "10.4.14"
  dependencies:
    "browserslist" "^4.21.5"
    "caniuse-lite" "^1.0.30001464"
    "fraction.js" "^4.2.0"
    "normalize-range" "^0.1.2"
    "picocolors" "^1.0.0"
    "postcss-value-parser" "^4.2.0"

"axios@^1.1.2":
  "integrity" "sha512-toYm+Bsyl6VC5wSkfkbbNB6ROv7KY93PEBBL6xyDczaIHasAiv4wPqQ/c4RjoQzipxRD2W5g21cOqQulZ7rHwQ=="
  "resolved" "https://registry.npmjs.org/axios/-/axios-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "follow-redirects" "^1.15.0"
    "form-data" "^4.0.0"
    "proxy-from-env" "^1.1.0"

"binary-extensions@^2.0.0":
  "integrity" "sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA=="
  "resolved" "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz"
  "version" "2.2.0"

"braces@^3.0.2", "braces@~3.0.2":
  "integrity" "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"browserslist@^4.21.5", "browserslist@>= 4.21.0":
  "integrity" "sha512-tUkiguQGW7S3IhB7N+c2MV/HZPSCPAAiYBZXLsBhFB/PCy6ZKKsZrmBayHV9fdGV/ARIfJ14NkxKzRDjvp7L6w=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-4.21.5.tgz"
  "version" "4.21.5"
  dependencies:
    "caniuse-lite" "^1.0.30001449"
    "electron-to-chromium" "^1.4.284"
    "node-releases" "^2.0.8"
    "update-browserslist-db" "^1.0.10"

"camelcase-css@^2.0.1":
  "integrity" "sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA=="
  "resolved" "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz"
  "version" "2.0.1"

"can-use-dom@^0.1.0":
  "integrity" "sha512-ceOhN1DL7Y4O6M0j9ICgmTYziV89WMd96SvSl0REd8PMgrY0B/WBOPoed5S1KUmJqXgUXh8gzSe6E3ae27upsQ=="
  "resolved" "https://registry.npmjs.org/can-use-dom/-/can-use-dom-0.1.0.tgz"
  "version" "0.1.0"

"caniuse-lite@^1.0.30001449", "caniuse-lite@^1.0.30001464":
  "integrity" "sha512-Rcp7221ScNqQPP3W+lVOYDyjdR6dC+neEQCttoNr5bAyz54AboB4iwpnWgyi8P4YUsPybVzT4LgWiBbI3drL4g=="
  "resolved" "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001469.tgz"
  "version" "1.0.30001469"

"chart.js@^2.9.4", "chart.js@>= 2.7.0 < 3":
  "integrity" "sha512-B07aAzxcrikjAPyV+01j7BmOpxtQETxTSlQ26BEYJ+3iUkbNKaOJ/nDbT6JjyqYxseM0ON12COHYdU2cTIjC7A=="
  "resolved" "https://registry.npmjs.org/chart.js/-/chart.js-2.9.4.tgz"
  "version" "2.9.4"
  dependencies:
    "chartjs-color" "^2.1.0"
    "moment" "^2.10.2"

"chartjs-color-string@^0.6.0":
  "integrity" "sha512-TIB5OKn1hPJvO7JcteW4WY/63v6KwEdt6udfnDE9iCAZgy+V4SrbSxoIbTw/xkUIapjEI4ExGtD0+6D3KyFd7A=="
  "resolved" "https://registry.npmjs.org/chartjs-color-string/-/chartjs-color-string-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "color-name" "^1.0.0"

"chartjs-color@^2.1.0":
  "integrity" "sha512-haqOg1+Yebys/Ts/9bLo/BqUcONQOdr/hoEr2LLTRl6C5LXctUdHxsCYfvQVg5JIxITrfCNUDr4ntqmQk9+/0w=="
  "resolved" "https://registry.npmjs.org/chartjs-color/-/chartjs-color-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "chartjs-color-string" "^0.6.0"
    "color-convert" "^1.9.3"

"chartjs-plugin-datalabels@^0.7.0":
  "integrity" "sha512-PKVUX14nYhH0wcdCpgOoC39Gbzvn6cZ7O9n+bwc02yKD9FTnJ7/TSrBcfebmolFZp1Rcicr9xbT0a5HUbigS7g=="
  "resolved" "https://registry.npmjs.org/chartjs-plugin-datalabels/-/chartjs-plugin-datalabels-0.7.0.tgz"
  "version" "0.7.0"

"chokidar@^3.5.3", "chokidar@>=3.0.0 <4.0.0":
  "integrity" "sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz"
  "version" "3.5.3"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"cleave.js@^1.6.0":
  "integrity" "sha512-ivqesy3j5hQVG3gywPfwKPbi/7ZSftY/UNp5uphnqjr25yI2CP8FS2ODQPzuLXXnNLi29e2+PgPkkiKUXLs/Nw=="
  "resolved" "https://registry.npmjs.org/cleave.js/-/cleave.js-1.6.0.tgz"
  "version" "1.6.0"

"color-convert@^1.9.3":
  "integrity" "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-name@^1.0.0", "color-name@^1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  "version" "1.1.3"

"combined-stream@^1.0.8":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"contra@1.9.4":
  "integrity" "sha512-N9ArHAqwR/lhPq4OdIAwH4e1btn6EIZMAz4TazjnzCiVECcWUPTma+dRAM38ERImEJBh8NiCCpjoQruSZ+agYg=="
  "resolved" "https://registry.npmjs.org/contra/-/contra-1.9.4.tgz"
  "version" "1.9.4"
  dependencies:
    "atoa" "1.0.0"
    "ticky" "1.0.1"

"core-js@^3.26.1":
  "integrity" "sha512-hQotSSARoNh1mYPi9O2YaWeiq/cEB95kOrFb4NCrO4RIFt1qqNpKsaE+vy/L3oiqvND5cThqXzUU3r9F7Efztg=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-3.30.0.tgz"
  "version" "3.30.0"

"country-select-js@^2.1.0":
  "integrity" "sha512-T7gM2MT6S06lGqqkkBCmWFlyryKuaBgbeJFFxZttT+GT6pwl63r5KuLQszkfbtL9YEu+8JvrRayfvyrZd9I++g=="
  "resolved" "https://registry.npmjs.org/country-select-js/-/country-select-js-2.1.0.tgz"
  "version" "2.1.0"

"crossvent@1.5.5":
  "integrity" "sha512-MY4xhBYEnVi+pmTpHCOCsCLYczc0PVtGdPBz6NXNXxikLaUZo4HdAeUb1UqAo3t3yXAloSelTmfxJ+/oUqkW5w=="
  "resolved" "https://registry.npmjs.org/crossvent/-/crossvent-1.5.5.tgz"
  "version" "1.5.5"
  dependencies:
    "custom-event" "^1.0.0"

"cssesc@^3.0.0":
  "integrity" "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg=="
  "resolved" "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"custom-event-polyfill@^1.0.7":
  "integrity" "sha512-TDDkd5DkaZxZFM8p+1I3yAlvM3rSr1wbrOliG4yJiwinMZN8z/iGL7BTlDkrJcYTmgUSb4ywVCc3ZaUtOtC76w=="
  "resolved" "https://registry.npmjs.org/custom-event-polyfill/-/custom-event-polyfill-1.0.7.tgz"
  "version" "1.0.7"

"custom-event@^1.0.0":
  "integrity" "sha512-GAj5FOq0Hd+RsCGVJxZuKaIDXDf3h6GQoNEjFgbLLI/trgtavwUbSnZ5pVfg27DVCaWjIohryS0JFwIJyT2cMg=="
  "resolved" "https://registry.npmjs.org/custom-event/-/custom-event-1.0.1.tgz"
  "version" "1.0.1"

"datatables.net-dt@^1.13.4":
  "integrity" "sha512-QAvuEej/qKSiaSmSeDQ36wWO72XzFGKkd0jdiqbp+2FHAAzIk+ffsqQAwylystMoBSiO0zlcdaqHoAPa5Dy7Pg=="
  "resolved" "https://registry.npmjs.org/datatables.net-dt/-/datatables.net-dt-1.13.4.tgz"
  "version" "1.13.4"
  dependencies:
    "datatables.net" ">=1.12.1"
    "jquery" ">=1.7"

"datatables.net@>=1.12.1":
  "integrity" "sha512-yzhArTOB6tPO2QFKm1z3hA4vabtt2hRvgw8XLsT1xqEirinfGYqWDiWXlkTPTaJv2e7gG+Kf985sXkzBFlGrGQ=="
  "resolved" "https://registry.npmjs.org/datatables.net/-/datatables.net-1.13.4.tgz"
  "version" "1.13.4"
  dependencies:
    "jquery" ">=1.7"

"deepmerge@^4.2.2":
  "integrity" "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A=="
  "resolved" "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
  "version" "4.3.1"

"defined@^1.0.0":
  "integrity" "sha512-hsBd2qSVCRE+5PmNdHt1uzyrFu5d3RwmFDKzyNZMFq/EwDNJF7Ee5+D5oEKF0hU6LhtoUF1macFvOe4AskQC1Q=="
  "resolved" "https://registry.npmjs.org/defined/-/defined-1.0.1.tgz"
  "version" "1.0.1"

"delayed-stream@~1.0.0":
  "integrity" "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"detect-autofill@^1.1.3":
  "integrity" "sha512-utCBQwCR/beSnADQmBC7C4tTueBBkYCl6WSpfGUkYKO/+MzPxqYGj6G4MvHzcKmH1gCTK+VunX2vaagvkRXPvA=="
  "resolved" "https://registry.npmjs.org/detect-autofill/-/detect-autofill-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "custom-event-polyfill" "^1.0.7"

"detective@^5.2.0", "detective@^5.2.1":
  "integrity" "sha512-v9XE1zRnz1wRtgurGu0Bs8uHKFSTdteYZNbIPFVhUZ39L/S79ppMpdmVOZAnoz1jfEFodc48n6MX483Xo3t1yw=="
  "resolved" "https://registry.npmjs.org/detective/-/detective-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "acorn-node" "^1.8.2"
    "defined" "^1.0.0"
    "minimist" "^1.2.6"

"didyoumean@^1.2.2":
  "integrity" "sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw=="
  "resolved" "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz"
  "version" "1.2.2"

"dlv@^1.1.3":
  "integrity" "sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA=="
  "resolved" "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz"
  "version" "1.1.3"

"dragula@^3.7.3":
  "integrity" "sha512-/rRg4zRhcpf81TyDhaHLtXt6sEywdfpv1cRUMeFFy7DuypH2U0WUL0GTdyAQvXegviT4PJK4KuMmOaIDpICseQ=="
  "resolved" "https://registry.npmjs.org/dragula/-/dragula-3.7.3.tgz"
  "version" "3.7.3"
  dependencies:
    "contra" "1.9.4"
    "crossvent" "1.5.5"

"dropzone@^6.0.0-beta.2":
  "integrity" "sha512-k44yLuFFhRk53M8zP71FaaNzJYIzr99SKmpbO/oZKNslDjNXQsBTdfLs+iONd0U0L94zzlFzRnFdqbLcs7h9fQ=="
  "resolved" "https://registry.npmjs.org/dropzone/-/dropzone-6.0.0-beta.2.tgz"
  "version" "6.0.0-beta.2"
  dependencies:
    "@swc/helpers" "^0.2.13"
    "just-extend" "^5.0.0"

"electron-to-chromium@^1.4.284":
  "integrity" "sha512-W8gdzXG86mVPoc56eM8YA+QiLxaAxJ8cmDjxZgfhLLWVvZQxyA918w5tX2JEWApZta45T1/sYcmFHTsTOUE3nw=="
  "resolved" "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.337.tgz"
  "version" "1.4.337"

"esbuild@^0.17.5":
  "integrity" "sha512-bX/zHl7Gn2CpQwcMtRogTTBf9l1nl+H6R8nUbjk+RuKqAE3+8FDulLA+pHvX7aA7Xe07Iwa+CWvy9I8Y2qqPKQ=="
  "resolved" "https://registry.npmjs.org/esbuild/-/esbuild-0.17.12.tgz"
  "version" "0.17.12"
  optionalDependencies:
    "@esbuild/android-arm" "0.17.12"
    "@esbuild/android-arm64" "0.17.12"
    "@esbuild/android-x64" "0.17.12"
    "@esbuild/darwin-arm64" "0.17.12"
    "@esbuild/darwin-x64" "0.17.12"
    "@esbuild/freebsd-arm64" "0.17.12"
    "@esbuild/freebsd-x64" "0.17.12"
    "@esbuild/linux-arm" "0.17.12"
    "@esbuild/linux-arm64" "0.17.12"
    "@esbuild/linux-ia32" "0.17.12"
    "@esbuild/linux-loong64" "0.17.12"
    "@esbuild/linux-mips64el" "0.17.12"
    "@esbuild/linux-ppc64" "0.17.12"
    "@esbuild/linux-riscv64" "0.17.12"
    "@esbuild/linux-s390x" "0.17.12"
    "@esbuild/linux-x64" "0.17.12"
    "@esbuild/netbsd-x64" "0.17.12"
    "@esbuild/openbsd-x64" "0.17.12"
    "@esbuild/sunos-x64" "0.17.12"
    "@esbuild/win32-arm64" "0.17.12"
    "@esbuild/win32-ia32" "0.17.12"
    "@esbuild/win32-x64" "0.17.12"

"escalade@^3.1.1":
  "integrity" "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw=="
  "resolved" "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz"
  "version" "3.1.1"

"fast-glob@^3.2.11", "fast-glob@^3.2.12":
  "integrity" "sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w=="
  "resolved" "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.12.tgz"
  "version" "3.2.12"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.4"

"fastq@^1.6.0":
  "integrity" "sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw=="
  "resolved" "https://registry.npmjs.org/fastq/-/fastq-1.15.0.tgz"
  "version" "1.15.0"
  dependencies:
    "reusify" "^1.0.4"

"fill-range@^7.0.1":
  "integrity" "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"follow-redirects@^1.15.0":
  "integrity" "sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA=="
  "resolved" "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.2.tgz"
  "version" "1.15.2"

"form-data@^4.0.0":
  "integrity" "sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "mime-types" "^2.1.12"

"fraction.js@^4.2.0":
  "integrity" "sha512-MhLuK+2gUcnZe8ZHlaaINnQLl0xRIGRfcGk2yl8xoQAfHrSsL3rYu6FCmBdkdbhc9EPlwyGHewaRsvwRMJtAlA=="
  "resolved" "https://registry.npmjs.org/fraction.js/-/fraction.js-4.2.0.tgz"
  "version" "4.2.0"

"function-bind@^1.1.1":
  "integrity" "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="
  "resolved" "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"glob-parent@^5.1.2", "glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@^6.0.2":
  "integrity" "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"has@^1.0.3":
  "integrity" "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
  "resolved" "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"iconify-icon@^1.0.1":
  "integrity" "sha512-MxaO3Jhf3f5ymPWGHR9x74f90TNKcq1D+B2iGucGhVtqAgbC9EtM06kKiTGH2CKELNnexckwhrA3/+OpT4HKFw=="
  "resolved" "https://registry.npmjs.org/iconify-icon/-/iconify-icon-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "@iconify/types" "^2.0.0"

"immutable@^4.0.0":
  "integrity" "sha512-0AOCmOip+xgJwEVTQj1EfiDDOkPmuyllDuTuEX+DDXUgapLAsBIfkg3sxCYyCEA8mQqZrrxPUGjcOQ2JS3WLkg=="
  "resolved" "https://registry.npmjs.org/immutable/-/immutable-4.3.0.tgz"
  "version" "4.3.0"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-core-module@^2.9.0":
  "integrity" "sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw=="
  "resolved" "https://registry.npmjs.org/is-core-module/-/is-core-module-2.11.0.tgz"
  "version" "2.11.0"
  dependencies:
    "has" "^1.0.3"

"is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-glob@^4.0.1", "is-glob@^4.0.3", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"jquery-validation@^1.19.5":
  "integrity" "sha512-X2SmnPq1mRiDecVYL8edWx+yTBZDyC8ohWXFhXdtqFHgU9Wd4KHkvcbCoIZ0JaSaumzS8s2gXSkP8F7ivg/8ZQ=="
  "resolved" "https://registry.npmjs.org/jquery-validation/-/jquery-validation-1.19.5.tgz"
  "version" "1.19.5"

"jquery@^1.7 || ^2.0 || ^3.1", "jquery@^3.6.4", "jquery@>=1.7", "jquery@>=1.8.3":
  "integrity" "sha512-v28EW9DWDFpzcD9O5iyJXg3R3+q+mET5JhnjJzQUZMHOv67bpSIHq81GEYpPNZHG+XXHsfSme3nxp/hndKEcsQ=="
  "resolved" "https://registry.npmjs.org/jquery/-/jquery-3.6.4.tgz"
  "version" "3.6.4"

"just-extend@^5.0.0":
  "integrity" "sha512-b+z6yF1d4EOyDgylzQo5IminlUmzSeqR1hs/bzjBNjuGras4FXq/6TrzjxfN0j+TmI0ltJzTNlqXUMCniciwKQ=="
  "resolved" "https://registry.npmjs.org/just-extend/-/just-extend-5.1.1.tgz"
  "version" "5.1.1"

"laravel-vite-plugin@^0.7.2":
  "integrity" "sha512-NlIuXbeuI+4NZzRpWNpGHRVTwuFWessvD7QoD+o2MlyAi7qyUS4J8r4/yTlu1dl9lxcR7iKoYUmHQqZDcrw2KA=="
  "resolved" "https://registry.npmjs.org/laravel-vite-plugin/-/laravel-vite-plugin-0.7.4.tgz"
  "version" "0.7.4"
  dependencies:
    "picocolors" "^1.0.0"
    "vite-plugin-full-reload" "^1.0.5"

"leaflet@^1.9.3":
  "integrity" "sha512-iB2cR9vAkDOu5l3HAay2obcUHZ7xwUBBjph8+PGtmW/2lYhbLizWtG7nTeYht36WfOslixQF9D/uSIzhZgGMfQ=="
  "resolved" "https://registry.npmjs.org/leaflet/-/leaflet-1.9.3.tgz"
  "version" "1.9.3"

"lilconfig@^2.0.5", "lilconfig@^2.0.6":
  "integrity" "sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ=="
  "resolved" "https://registry.npmjs.org/lilconfig/-/lilconfig-2.1.0.tgz"
  "version" "2.1.0"

"loadjs@^4.2.0":
  "integrity" "sha512-AgQGZisAlTPbTEzrHPb6q+NYBMD+DP9uvGSIjSUM5uG+0jG15cb8axWpxuOIqrmQjn6scaaH8JwloiP27b2KXA=="
  "resolved" "https://registry.npmjs.org/loadjs/-/loadjs-4.2.0.tgz"
  "version" "4.2.0"

"lodash-es@^4.17.21":
  "integrity" "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw=="
  "resolved" "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz"
  "version" "4.17.21"

"lodash.castarray@^4.4.0":
  "integrity" "sha512-aVx8ztPv7/2ULbArGJ2Y42bG1mEQ5mGjpdvrbJcJFU3TbYybe+QlLS4pst9zV52ymy2in1KpFPiZnAOATxD4+Q=="
  "resolved" "https://registry.npmjs.org/lodash.castarray/-/lodash.castarray-4.4.0.tgz"
  "version" "4.4.0"

"lodash.isplainobject@^4.0.6":
  "integrity" "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA=="
  "resolved" "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  "version" "4.0.6"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash@^4.17.19", "lodash@^4.17.21":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"merge2@^1.3.0":
  "integrity" "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg=="
  "resolved" "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  "version" "1.4.1"

"micromatch@^4.0.4", "micromatch@^4.0.5":
  "integrity" "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA=="
  "resolved" "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz"
  "version" "4.0.5"
  dependencies:
    "braces" "^3.0.2"
    "picomatch" "^2.3.1"

"mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@^2.1.12":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mini-svg-data-uri@^1.2.3":
  "integrity" "sha512-r9deDe9p5FJUPZAk3A59wGH7Ii9YrjjWw0jmw/liSbHl2CHiyXj6FcDXDu2K3TjVAXqiJdaw3xxwlZZr9E6nHg=="
  "resolved" "https://registry.npmjs.org/mini-svg-data-uri/-/mini-svg-data-uri-1.4.4.tgz"
  "version" "1.4.4"

"minimist@^1.2.6":
  "integrity" "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA=="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
  "version" "1.2.8"

"moment@^2.10.2":
  "integrity" "sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w=="
  "resolved" "https://registry.npmjs.org/moment/-/moment-2.29.4.tgz"
  "version" "2.29.4"

"nanoid@^3.3.4":
  "integrity" "sha512-MqBkQh/OHTS2egovRtLk45wEyNXwF+cokD+1YPf9u5VfJiRdAiRwB2froX5Co9Rh20xs4siNPm8naNotSD6RBw=="
  "resolved" "https://registry.npmjs.org/nanoid/-/nanoid-3.3.4.tgz"
  "version" "3.3.4"

"node-releases@^2.0.8":
  "integrity" "sha512-5GFldHPXVG/YZmFzJvKK2zDSzPKhEp0+ZR5SVaoSag9fsL5YgHbUHDfnG5494ISANDcK4KwPXAx2xqVEydmd7w=="
  "resolved" "https://registry.npmjs.org/node-releases/-/node-releases-2.0.10.tgz"
  "version" "2.0.10"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA=="
  "resolved" "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"object-hash@^3.0.0":
  "integrity" "sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw=="
  "resolved" "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz"
  "version" "3.0.0"

"owl.carousel@^2.3.4":
  "integrity" "sha512-JaDss9+feAvEW8KZppPSpllfposEzQiW+Ytt/Xm5t/3CTJ7YVmkh6RkWixoA2yXk2boIwedYxOvrrppIGzru9A=="
  "resolved" "https://registry.npmjs.org/owl.carousel/-/owl.carousel-2.3.4.tgz"
  "version" "2.3.4"
  dependencies:
    "jquery" ">=1.8.3"

"path-parse@^1.0.7":
  "integrity" "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="
  "resolved" "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"perfect-scrollbar@^1.5.0":
  "integrity" "sha512-dzalfutyP3e/FOpdlhVryN4AJ5XDVauVWxybSkLZmakFE2sS3y3pc4JnSprw8tGmHvkaG5Edr5T7LBTZ+WWU2g=="
  "resolved" "https://registry.npmjs.org/perfect-scrollbar/-/perfect-scrollbar-1.5.5.tgz"
  "version" "1.5.5"

"picocolors@^1.0.0":
  "integrity" "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ=="
  "resolved" "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz"
  "version" "1.0.0"

"picomatch@^2.0.4", "picomatch@^2.2.1", "picomatch@^2.3.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pify@^2.3.0":
  "integrity" "sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog=="
  "resolved" "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  "version" "2.3.0"

"plyr@^3.7.8":
  "integrity" "sha512-yG/EHDobwbB/uP+4Bm6eUpJ93f8xxHjjk2dYcD1Oqpe1EcuQl5tzzw9Oq+uVAzd2lkM11qZfydSiyIpiB8pgdA=="
  "resolved" "https://registry.npmjs.org/plyr/-/plyr-3.7.8.tgz"
  "version" "3.7.8"
  dependencies:
    "core-js" "^3.26.1"
    "custom-event-polyfill" "^1.0.7"
    "loadjs" "^4.2.0"
    "rangetouch" "^2.0.1"
    "url-polyfill" "^1.1.12"

"popper.js@^1.16.1":
  "integrity" "sha512-Wb4p1J4zyFTbM+u6WuO4XstYx4Ky9Cewe4DWrel7B0w6VVICvPwdOpotjzcf6eD8TsckVnIMNONQyPIUFOUbCQ=="
  "resolved" "https://registry.npmjs.org/popper.js/-/popper.js-1.16.1.tgz"
  "version" "1.16.1"

"postcss-import@^14.1.0":
  "integrity" "sha512-flwI+Vgm4SElObFVPpTIT7SU7R3qk2L7PyduMcokiaVKuWv9d/U+Gm/QAd8NDLuykTWTkcrjOeD2Pp1rMeBTGw=="
  "resolved" "https://registry.npmjs.org/postcss-import/-/postcss-import-14.1.0.tgz"
  "version" "14.1.0"
  dependencies:
    "postcss-value-parser" "^4.0.0"
    "read-cache" "^1.0.0"
    "resolve" "^1.1.7"

"postcss-js@^4.0.0":
  "integrity" "sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw=="
  "resolved" "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "camelcase-css" "^2.0.1"

"postcss-load-config@^3.1.4":
  "integrity" "sha512-6DiM4E7v4coTE4uzA8U//WhtPwyhiim3eyjEMFCnUpzbrkK9wJHgKDT2mR+HbtSrd/NubVaYTOpSpjUl8NQeRg=="
  "resolved" "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "lilconfig" "^2.0.5"
    "yaml" "^1.10.2"

"postcss-nested@5.0.6":
  "integrity" "sha512-rKqm2Fk0KbA8Vt3AdGN0FB9OBOMDVajMG6ZCf/GoHgdxUJ4sBFp0A/uMIRm+MJUdo33YXEtjqIz8u7DAp8B7DA=="
  "resolved" "https://registry.npmjs.org/postcss-nested/-/postcss-nested-5.0.6.tgz"
  "version" "5.0.6"
  dependencies:
    "postcss-selector-parser" "^6.0.6"

"postcss-nested@6.0.0":
  "integrity" "sha512-0DkamqrPcmkBDsLn+vQDIrtkSbNkv5AD/M322ySo9kqFkCIYklym2xEmWkwo+Y3/qZo34tzEPNUw4y7yMCdv5w=="
  "resolved" "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "postcss-selector-parser" "^6.0.10"

"postcss-selector-parser@^6.0.10", "postcss-selector-parser@^6.0.6", "postcss-selector-parser@6.0.10":
  "integrity" "sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.10.tgz"
  "version" "6.0.10"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-selector-parser@^6.0.11":
  "integrity" "sha512-zbARubNdogI9j7WY4nQJBiNqQf3sLS3wCP4WfOidu+p28LofJqDH1tcXypGrcmMHhDk2t9wGhCsYe/+szLTy1g=="
  "resolved" "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.11.tgz"
  "version" "6.0.11"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-value-parser@^4.0.0", "postcss-value-parser@^4.2.0":
  "integrity" "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="
  "resolved" "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"postcss@^8.0.0", "postcss@^8.0.9", "postcss@^8.1.0", "postcss@^8.2.14", "postcss@^8.4.12", "postcss@^8.4.21", "postcss@>=8.0.9":
  "integrity" "sha512-tP7u/Sn/dVxK2NnruI4H9BG+x+Wxz6oeZ1cJ8P6G/PZY0IKk4k/63TDsQf2kQq3+qoJeLm2kIBUNlZe3zgb4Zg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-8.4.21.tgz"
  "version" "8.4.21"
  dependencies:
    "nanoid" "^3.3.4"
    "picocolors" "^1.0.0"
    "source-map-js" "^1.0.2"

"preact@~10.12.1":
  "integrity" "sha512-l8386ixSsBdbreOAkqtrwqHwdvR35ID8c3rKPa8lCWuO86dBi32QWHV4vfsZK1utLLFMvw+Z5Ad4XLkZzchscg=="
  "resolved" "https://registry.npmjs.org/preact/-/preact-10.12.1.tgz"
  "version" "10.12.1"

"proxy-from-env@^1.1.0":
  "integrity" "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="
  "resolved" "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  "version" "1.1.0"

"queue-microtask@^1.2.2":
  "integrity" "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A=="
  "resolved" "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"quick-lru@^5.1.1":
  "integrity" "sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA=="
  "resolved" "https://registry.npmjs.org/quick-lru/-/quick-lru-5.1.1.tgz"
  "version" "5.1.1"

"rangetouch@^2.0.1":
  "integrity" "sha512-sln+pNSc8NGaHoLzwNBssFSf/rSYkqeBXzX1AtJlkJiUaVSJSbRAWJk+4omsXkN+EJalzkZhWQ3th1m0FpR5xA=="
  "resolved" "https://registry.npmjs.org/rangetouch/-/rangetouch-2.0.1.tgz"
  "version" "2.0.1"

"read-cache@^1.0.0":
  "integrity" "sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA=="
  "resolved" "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "pify" "^2.3.0"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"resize-observer-polyfill@^1.5.1":
  "integrity" "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="
  "resolved" "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve@^1.1.7", "resolve@^1.22.0", "resolve@^1.22.1":
  "integrity" "sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw=="
  "resolved" "https://registry.npmjs.org/resolve/-/resolve-1.22.1.tgz"
  "version" "1.22.1"
  dependencies:
    "is-core-module" "^2.9.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"reusify@^1.0.4":
  "integrity" "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw=="
  "resolved" "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  "version" "1.0.4"

"rollup@^3.18.0":
  "integrity" "sha512-sz2w8cBJlWQ2E17RcpvHuf4sk2BQx4tfKDnjNPikEpLEevrbIAR7CH3PGa2hpPwWbNgPaA9yh9Jzljds5bc9zg=="
  "resolved" "https://registry.npmjs.org/rollup/-/rollup-3.20.1.tgz"
  "version" "3.20.1"
  optionalDependencies:
    "fsevents" "~2.3.2"

"run-parallel@^1.1.9":
  "integrity" "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA=="
  "resolved" "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"sass@*", "sass@^1.59.3":
  "integrity" "sha512-QCq98N3hX1jfTCoUAsF3eyGuXLsY7BCnCEg9qAact94Yc21npG2/mVOqoDvE0fCbWDqiM4WlcJQla0gWG2YlxQ=="
  "resolved" "https://registry.npmjs.org/sass/-/sass-1.59.3.tgz"
  "version" "1.59.3"
  dependencies:
    "chokidar" ">=3.0.0 <4.0.0"
    "immutable" "^4.0.0"
    "source-map-js" ">=0.6.2 <2.0.0"

"simplebar-core@^1.2.2":
  "integrity" "sha512-uVNCnLtZRHgzBd5Tg23hGA5LX2NBt15xPQ3UhPUbPRnSx3rEYuWLK3iEnkR8Yxkcp/Kf/Vu/PJTEaiaJ3T0gTw=="
  "resolved" "https://registry.npmjs.org/simplebar-core/-/simplebar-core-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "can-use-dom" "^0.1.0"
    "lodash" "^4.17.21"
    "lodash-es" "^4.17.21"

"simplebar@^6.2.3":
  "integrity" "sha512-meFgVBAshIHXlrz0vYckqwEUJ1EgyWPm1Stzho77uY16SrPEj9Gf6778hT6AA/Bgal2q2/O8tncu6UY3UhQ6Mw=="
  "resolved" "https://registry.npmjs.org/simplebar/-/simplebar-6.2.3.tgz"
  "version" "6.2.3"
  dependencies:
    "can-use-dom" "^0.1.0"
    "simplebar-core" "^1.2.2"

"source-map-js@^1.0.2", "source-map-js@>=0.6.2 <2.0.0":
  "integrity" "sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw=="
  "resolved" "https://registry.npmjs.org/source-map-js/-/source-map-js-1.0.2.tgz"
  "version" "1.0.2"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="
  "resolved" "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"svg.draggable.js@^2.2.2":
  "integrity" "sha512-JzNHBc2fLQMzYCZ90KZHN2ohXL0BQJGQimK1kGk6AvSeibuKcIdDX9Kr0dT9+UJ5O8nYA0RB839Lhvk4CY4MZw=="
  "resolved" "https://registry.npmjs.org/svg.draggable.js/-/svg.draggable.js-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "svg.js" "^2.0.1"

"svg.easing.js@^2.0.0":
  "integrity" "sha512-//ctPdJMGy22YoYGV+3HEfHbm6/69LJUTAqI2/5qBvaNHZ9uUFVC82B0Pl299HzgH13rKrBgi4+XyXXyVWWthA=="
  "resolved" "https://registry.npmjs.org/svg.easing.js/-/svg.easing.js-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "svg.js" ">=2.3.x"

"svg.filter.js@^2.0.2":
  "integrity" "sha512-xkGBwU+dKBzqg5PtilaTb0EYPqPfJ9Q6saVldX+5vCRy31P6TlRCP3U9NxH3HEufkKkpNgdTLBJnmhDHeTqAkw=="
  "resolved" "https://registry.npmjs.org/svg.filter.js/-/svg.filter.js-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "svg.js" "^2.2.5"

"svg.js@^2.0.1", "svg.js@^2.2.5", "svg.js@^2.4.0", "svg.js@^2.6.5", "svg.js@>=2.3.x":
  "integrity" "sha512-ycbxpizEQktk3FYvn/8BH+6/EuWXg7ZpQREJvgacqn46gIddG24tNNe4Son6omdXCnSOaApnpZw6MPCBA1dODA=="
  "resolved" "https://registry.npmjs.org/svg.js/-/svg.js-2.7.1.tgz"
  "version" "2.7.1"

"svg.pathmorphing.js@^0.1.3":
  "integrity" "sha512-49HWI9X4XQR/JG1qXkSDV8xViuTLIWm/B/7YuQELV5KMOPtXjiwH4XPJvr/ghEDibmLQ9Oc22dpWpG0vUDDNww=="
  "resolved" "https://registry.npmjs.org/svg.pathmorphing.js/-/svg.pathmorphing.js-0.1.3.tgz"
  "version" "0.1.3"
  dependencies:
    "svg.js" "^2.4.0"

"svg.resize.js@^1.4.3":
  "integrity" "sha512-9k5sXJuPKp+mVzXNvxz7U0uC9oVMQrrf7cFsETznzUDDm0x8+77dtZkWdMfRlmbkEEYvUn9btKuZ3n41oNA+uw=="
  "resolved" "https://registry.npmjs.org/svg.resize.js/-/svg.resize.js-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "svg.js" "^2.6.5"
    "svg.select.js" "^2.1.2"

"svg.select.js@^2.1.2":
  "integrity" "sha512-tH6ABEyJsAOVAhwcCjF8mw4crjXSI1aa7j2VQR8ZuJ37H2MBUbyeqYr5nEO7sSN3cy9AR9DUwNg0t/962HlDbQ=="
  "resolved" "https://registry.npmjs.org/svg.select.js/-/svg.select.js-2.1.2.tgz"
  "version" "2.1.2"
  dependencies:
    "svg.js" "^2.2.5"

"svg.select.js@^3.0.1":
  "integrity" "sha512-h5IS/hKkuVCbKSieR9uQCj9w+zLHoPh+ce19bBYyqF53g6mnPB8sAtIbe1s9dh2S2fCmYX2xel1Ln3PJBbK4kw=="
  "resolved" "https://registry.npmjs.org/svg.select.js/-/svg.select.js-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "svg.js" "^2.6.5"

"sweetalert2@^11.6.10":
  "integrity" "sha512-fUN/fyVSBZNtY4Rr/Qtxn7tNNnlRAbUhQxTQ9uOo0xVMIHBmqq4/9pau5N9dB2pvkB353XL/ywRAycscLoYU3w=="
  "resolved" "https://registry.npmjs.org/sweetalert2/-/sweetalert2-11.7.3.tgz"
  "version" "11.7.3"

"tailwindcss@^3.2.7", "tailwindcss@>=2.0.0 || >=3.0.0 || >=3.0.0-alpha.1", "tailwindcss@>=3.0.0 || >= 3.0.0-alpha.1", "tailwindcss@>=3.0.0 || insiders":
  "integrity" "sha512-B6DLqJzc21x7wntlH/GsZwEXTBttVSl1FtCzC8WP4oBc/NKef7kaax5jeihkkCEWc831/5NDJ9gRNDK6NEioQQ=="
  "resolved" "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "arg" "^5.0.2"
    "chokidar" "^3.5.3"
    "color-name" "^1.1.4"
    "detective" "^5.2.1"
    "didyoumean" "^1.2.2"
    "dlv" "^1.1.3"
    "fast-glob" "^3.2.12"
    "glob-parent" "^6.0.2"
    "is-glob" "^4.0.3"
    "lilconfig" "^2.0.6"
    "micromatch" "^4.0.5"
    "normalize-path" "^3.0.0"
    "object-hash" "^3.0.0"
    "picocolors" "^1.0.0"
    "postcss" "^8.0.9"
    "postcss-import" "^14.1.0"
    "postcss-js" "^4.0.0"
    "postcss-load-config" "^3.1.4"
    "postcss-nested" "6.0.0"
    "postcss-selector-parser" "^6.0.11"
    "postcss-value-parser" "^4.2.0"
    "quick-lru" "^5.1.1"
    "resolve" "^1.22.1"

"tailwindcss@~3.0.7":
  "integrity" "sha512-H3uMmZNWzG6aqmg9q07ZIRNIawoiEcNFKDfL+YzOPuPsXuDXxJxB9icqzLgdzKNwjG3SAro2h9SYav8ewXNgig=="
  "resolved" "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.0.24.tgz"
  "version" "3.0.24"
  dependencies:
    "arg" "^5.0.1"
    "chokidar" "^3.5.3"
    "color-name" "^1.1.4"
    "detective" "^5.2.0"
    "didyoumean" "^1.2.2"
    "dlv" "^1.1.3"
    "fast-glob" "^3.2.11"
    "glob-parent" "^6.0.2"
    "is-glob" "^4.0.3"
    "lilconfig" "^2.0.5"
    "normalize-path" "^3.0.0"
    "object-hash" "^3.0.0"
    "picocolors" "^1.0.0"
    "postcss" "^8.4.12"
    "postcss-js" "^4.0.0"
    "postcss-load-config" "^3.1.4"
    "postcss-nested" "5.0.6"
    "postcss-selector-parser" "^6.0.10"
    "postcss-value-parser" "^4.2.0"
    "quick-lru" "^5.1.1"
    "resolve" "^1.22.0"

"ticky@1.0.1":
  "integrity" "sha512-RX35iq/D+lrsqhcPWIazM9ELkjOe30MSeoBHQHSsRwd1YuhJO5ui1K1/R0r7N3mFvbLBs33idw+eR6j+w6i/DA=="
  "resolved" "https://registry.npmjs.org/ticky/-/ticky-1.0.1.tgz"
  "version" "1.0.1"

"tippy.js@^6.3.7":
  "integrity" "sha512-E1d3oP2emgJ9dRQZdf3Kkn0qJgI6ZLpyS5z6ZkY1DF3kaQaBsGZsndEpHwx+eC+tYM41HaSNvNtLx8tU57FzTQ=="
  "resolved" "https://registry.npmjs.org/tippy.js/-/tippy.js-6.3.7.tgz"
  "version" "6.3.7"
  dependencies:
    "@popperjs/core" "^2.9.0"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"tw-elements@1.0.0-alpha13":
  "integrity" "sha512-lz1D583ZGDF4s8e89dmXkhfD8m2abgAlaK8/J6cAEm3DLxz7RtqKdunzja6xcKxDZO3bXEd6oGNdQ5QHpyCqrg=="
  "resolved" "https://registry.npmjs.org/tw-elements/-/tw-elements-1.0.0-alpha13.tgz"
  "version" "1.0.0-alpha13"
  dependencies:
    "@popperjs/core" "^2.6.0"
    "chart.js" "^2.9.4"
    "chartjs-plugin-datalabels" "^0.7.0"
    "deepmerge" "^4.2.2"
    "detect-autofill" "^1.1.3"
    "perfect-scrollbar" "^1.5.0"
    "popper.js" "^1.16.1"
    "tailwindcss" "~3.0.7"

"update-browserslist-db@^1.0.10":
  "integrity" "sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ=="
  "resolved" "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "escalade" "^3.1.1"
    "picocolors" "^1.0.0"

"url-polyfill@^1.1.12":
  "integrity" "sha512-mYFmBHCapZjtcNHW0MDq9967t+z4Dmg5CJ0KqysK3+ZbyoNOWQHksGCTWwDhxGXllkWlOc10Xfko6v4a3ucM6A=="
  "resolved" "https://registry.npmjs.org/url-polyfill/-/url-polyfill-1.1.12.tgz"
  "version" "1.1.12"

"util-deprecate@^1.0.2":
  "integrity" "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"vite-plugin-full-reload@^1.0.5":
  "integrity" "sha512-kVZFDFWr0DxiHn6MuDVTQf7gnWIdETGlZh0hvTiMXzRN80vgF4PKbONSq8U1d0WtHsKaFODTQgJeakLacoPZEQ=="
  "resolved" "https://registry.npmjs.org/vite-plugin-full-reload/-/vite-plugin-full-reload-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "picocolors" "^1.0.0"
    "picomatch" "^2.3.1"

"vite@^2 || ^3 || ^4", "vite@^3.0.0 || ^4.0.0", "vite@^4.0.0":
  "integrity" "sha512-7MKhqdy0ISo4wnvwtqZkjke6XN4taqQ2TBaTccLIpOKv7Vp2h4Y+NpmWCnGDeSvvn45KxvWgGyb0MkHvY1vgbg=="
  "resolved" "https://registry.npmjs.org/vite/-/vite-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "esbuild" "^0.17.5"
    "postcss" "^8.4.21"
    "resolve" "^1.22.1"
    "rollup" "^3.18.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"xtend@^4.0.2":
  "integrity" "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="
  "resolved" "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  "version" "4.0.2"

"yaml@^1.10.2":
  "integrity" "sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg=="
  "resolved" "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"
  "version" "1.10.2"
